"""
任务表格组件
负责显示和管理任务列表
"""

from typing import List, Optional
from PyQt6.QtWidgets import (
    QTableWidget, QTableWidgetItem, QHeaderView, QMenu, QAbstractItemView
)
from PyQt6.QtCore import Qt, pyqtSignal, QPoint
from PyQt6.QtGui import QFont, QColor

# 项目导入
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from core.task_scanner import TaskInfo
from core.config_manager import ConfigManager
from ..styles.table_styles import TableStyles


class TaskTableWidget(QTableWidget):
    """任务表格组件
    
    特性：
    - 美观的现代化样式
    - 状态颜色区分
    - 右键菜单支持
    - 任务选择信号
    - 自动列宽调整
    """
    
    # 自定义信号
    task_selected = pyqtSignal(TaskInfo)  # 任务被选中
    task_context_menu_requested = pyqtSignal(TaskInfo, QPoint)  # 右键菜单请求
    task_double_clicked = pyqtSignal(TaskInfo)  # 任务被双击
    
    def __init__(self):
        """初始化任务表格"""
        super().__init__()
        self.tasks_data: List[TaskInfo] = []
        self.config_manager: Optional[ConfigManager] = None
        
        self._setup_table()
        self._setup_headers()
        self._setup_styles()
        self._setup_connections()
    
    def _setup_table(self) -> None:
        """设置表格基础配置"""
        # 设置列数和表头
        headers = ["任务名称", "状态", "频率(分钟)", "下次运行时间", "运行次数", "最后运行时间"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        
        # 启用排序
        self.setSortingEnabled(True)
        
        # 右键菜单
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
    
    def _setup_headers(self) -> None:
        """设置表头"""
        header = self.horizontalHeader()
        
        # 设置列宽策略
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # 任务名称自适应
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)    # 状态固定
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)    # 频率固定
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)    # 下次运行时间固定
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)    # 运行次数固定
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)    # 最后运行时间固定
        
        # 设置具体列宽
        self.setColumnWidth(1, 100)  # 状态
        self.setColumnWidth(2, 80)   # 频率
        self.setColumnWidth(3, 150)  # 下次运行时间
        self.setColumnWidth(4, 70)   # 运行次数
        self.setColumnWidth(5, 150)  # 最后运行时间
        
        # 设置行号
        self.verticalHeader().setVisible(True)
        self.verticalHeader().setDefaultSectionSize(35)
        self.verticalHeader().setMinimumSectionSize(35)
    
    def _setup_styles(self) -> None:
        """设置表格样式"""
        # 应用主表格样式
        self.setStyleSheet(TableStyles.get_complete_style())
        
        # 设置行号样式
        self.verticalHeader().setStyleSheet(TableStyles.VERTICAL_HEADER)
    
    def _setup_connections(self) -> None:
        """设置信号连接"""
        self.itemClicked.connect(self._on_item_clicked)
        self.itemDoubleClicked.connect(self._on_item_double_clicked)
        self.customContextMenuRequested.connect(self._on_context_menu_requested)
    
    def load_tasks(self, tasks: List[TaskInfo], config_manager: ConfigManager) -> None:
        """加载任务数据到表格
        
        Args:
            tasks: 任务列表
            config_manager: 配置管理器
        """
        self.tasks_data = tasks
        self.config_manager = config_manager
        
        # 设置行数
        self.setRowCount(len(tasks))
        
        # 设置行号
        row_labels = [str(i + 1) for i in range(len(tasks))]
        self.setVerticalHeaderLabels(row_labels)
        
        # 填充数据
        for row, task in enumerate(tasks):
            self._populate_row(row, task)
    
    def _populate_row(self, row: int, task: TaskInfo) -> None:
        """填充单行数据
        
        Args:
            row: 行号
            task: 任务信息
        """
        if not self.config_manager:
            return
        
        config = self.config_manager.get_task_config(task.name)
        
        # 任务名称
        name_item = QTableWidgetItem(task.name)
        self.setItem(row, 0, name_item)
        
        # 状态（带颜色）
        status_item = self._create_status_item(config.status)
        self.setItem(row, 1, status_item)
        
        # 频率
        freq_item = QTableWidgetItem(str(config.frequency_minutes))
        freq_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setItem(row, 2, freq_item)
        
        # 下次运行时间
        next_run = config.next_run_time if config.next_run_time else "未设置"
        next_run_item = QTableWidgetItem(next_run)
        self.setItem(row, 3, next_run_item)
        
        # 运行次数
        count_item = QTableWidgetItem(str(config.run_count))
        count_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setItem(row, 4, count_item)
        
        # 最后运行时间
        last_run = config.last_run_time if config.last_run_time else "从未运行"
        last_run_item = QTableWidgetItem(last_run)
        self.setItem(row, 5, last_run_item)
    
    def _create_status_item(self, status: str) -> QTableWidgetItem:
        """创建状态显示项
        
        Args:
            status: 状态字符串
            
        Returns:
            格式化的状态表格项
        """
        # 状态图标映射
        status_icons = {
            "执行中": "🟢 执行中",
            "等待中": "🔵 等待中", 
            "已禁用": "🔴 已禁用"
        }
        
        display_text = status_icons.get(status, f"⚪ {status}")
        status_item = QTableWidgetItem(display_text)
        status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 设置颜色样式
        self._apply_status_style(status_item, status)
        
        return status_item
    
    def _apply_status_style(self, item: QTableWidgetItem, status: str) -> None:
        """应用状态样式
        
        Args:
            item: 表格项
            status: 状态字符串
        """
        # 颜色映射
        color_map = {
            "执行中": {"bg": "#dcfce7", "fg": "#166534"},
            "等待中": {"bg": "#dbeafe", "fg": "#1e40af"},
            "已禁用": {"bg": "#fef2f2", "fg": "#991b1b"}
        }
        
        colors = color_map.get(status, color_map["等待中"])
        
        # 应用颜色
        item.setBackground(QColor(colors["bg"]))
        item.setForeground(QColor(colors["fg"]))
        
        # 设置粗体字体
        font = QFont()
        font.setBold(True)
        font.setPointSize(9)
        item.setFont(font)
    
    def _on_item_clicked(self, item: QTableWidgetItem) -> None:
        """处理项点击事件"""
        row = item.row()
        if 0 <= row < len(self.tasks_data):
            task = self.tasks_data[row]
            self.task_selected.emit(task)
    
    def _on_item_double_clicked(self, item: QTableWidgetItem) -> None:
        """处理项双击事件"""
        row = item.row()
        if 0 <= row < len(self.tasks_data):
            task = self.tasks_data[row]
            self.task_double_clicked.emit(task)
    
    def _on_context_menu_requested(self, position: QPoint) -> None:
        """处理右键菜单请求"""
        item = self.itemAt(position)
        if item is None:
            return
        
        row = item.row()
        if 0 <= row < len(self.tasks_data):
            task = self.tasks_data[row]
            global_position = self.mapToGlobal(position)
            self.task_context_menu_requested.emit(task, global_position)
    
    def get_selected_task(self) -> Optional[TaskInfo]:
        """获取当前选中的任务
        
        Returns:
            选中的任务，如果没有选中则返回None
        """
        current_row = self.currentRow()
        if 0 <= current_row < len(self.tasks_data):
            return self.tasks_data[current_row]
        return None
    
    def refresh_task_status(self, task_name: str) -> None:
        """刷新特定任务的状态显示
        
        Args:
            task_name: 任务名称
        """
        if not self.config_manager:
            return
        
        # 找到对应的行
        for row in range(self.rowCount()):
            if row < len(self.tasks_data) and self.tasks_data[row].name == task_name:
                config = self.config_manager.get_task_config(task_name)
                
                # 更新状态列
                status_item = self._create_status_item(config.status)
                self.setItem(row, 1, status_item)
                
                # 更新其他可能变化的列
                self.item(row, 2).setText(str(config.frequency_minutes))
                self.item(row, 3).setText(config.next_run_time or "未设置")
                self.item(row, 4).setText(str(config.run_count))
                self.item(row, 5).setText(config.last_run_time or "从未运行")
                
                break
    
    def highlight_task(self, task_name: str) -> None:
        """高亮显示指定任务
        
        Args:
            task_name: 任务名称
        """
        for row in range(self.rowCount()):
            if row < len(self.tasks_data) and self.tasks_data[row].name == task_name:
                self.selectRow(row)
                self.scrollToItem(self.item(row, 0))
                break 