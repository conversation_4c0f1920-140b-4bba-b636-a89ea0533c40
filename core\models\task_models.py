"""
任务数据模型 - 定义任务相关的所有数据结构
"""

from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Optional, Dict, Any, List
from datetime import datetime
from pathlib import Path


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "等待中"
    RUNNING = "执行中" 
    DISABLED = "已禁用"
    COMPLETED = "已完成"
    FAILED = "执行失败"
    PAUSED = "已暂停"


class TaskCategory(Enum):
    """任务分类枚举"""
    DEFAULT = "默认"
    DATA_PROCESSING = "数据处理"
    SYSTEM_MAINTENANCE = "系统维护"
    MONITORING = "监控检查"
    BACKUP = "备份任务"
    NOTIFICATION = "通知任务"
    CUSTOM = "自定义"


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class TaskMetadata:
    """任务元数据"""
    name: str
    description: str = "无描述"
    author: str = "未知"
    version: str = "1.0.0"
    category: TaskCategory = TaskCategory.DEFAULT
    priority: TaskPriority = TaskPriority.NORMAL
    tags: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    timeout_seconds: int = 3600  # 默认1小时超时
    retry_count: int = 3
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass  
class TaskInfo:
    """任务信息数据类"""
    name: str
    file_path: Path
    metadata: TaskMetadata
    is_enabled: bool = True
    last_modified: float = 0.0
    file_size: int = 0
    checksum: str = ""
    
    def __post_init__(self):
        """后初始化处理"""
        if isinstance(self.file_path, str):
            self.file_path = Path(self.file_path)


@dataclass
class TaskConfig:
    """任务配置数据类"""
    name: str
    status: TaskStatus = TaskStatus.PENDING
    frequency_minutes: int = 60
    next_run_time: Optional[datetime] = None
    run_count: int = 0
    last_run_time: Optional[datetime] = None
    last_run_duration: float = 0.0
    success_count: int = 0
    failure_count: int = 0
    is_enabled: bool = True
    max_retries: int = 3
    current_retries: int = 0
    environment_vars: Dict[str, str] = field(default_factory=dict)
    command_args: List[str] = field(default_factory=list)
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        total_runs = self.success_count + self.failure_count
        if total_runs == 0:
            return 0.0
        return self.success_count / total_runs * 100.0


@dataclass
class TaskValidationResult:
    """任务验证结果"""
    is_valid: bool
    has_main_function: bool = False
    has_run_function: bool = False  
    has_execute_function: bool = False
    has_task_class: bool = False
    detected_functions: List[str] = field(default_factory=list)
    validation_errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    syntax_valid: bool = True
    imports_valid: bool = True
    
    @property
    def entry_points(self) -> List[str]:
        """获取可用的入口点"""
        entry_points = []
        if self.has_main_function:
            entry_points.append("main")
        if self.has_run_function:
            entry_points.append("run")
        if self.has_execute_function:
            entry_points.append("execute")
        if self.has_task_class:
            entry_points.append("Task")
        return entry_points
    
    @property
    def is_executable(self) -> bool:
        """判断任务是否可执行"""
        return (self.is_valid and 
                self.syntax_valid and 
                len(self.entry_points) > 0)


@dataclass
class TaskExecutionResult:
    """任务执行结果"""
    task_name: str
    status: TaskStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: float = 0.0
    return_value: Any = None
    output: str = ""
    error_message: str = ""
    exception_type: Optional[str] = None
    exit_code: int = 0
    memory_usage: float = 0.0
    cpu_usage: float = 0.0
    
    @property
    def is_success(self) -> bool:
        """判断执行是否成功"""
        return self.status == TaskStatus.COMPLETED and self.exit_code == 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'task_name': self.task_name,
            'status': self.status.value,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration': self.duration,
            'return_value': str(self.return_value) if self.return_value else None,
            'output': self.output,
            'error_message': self.error_message,
            'exception_type': self.exception_type,
            'exit_code': self.exit_code,
            'memory_usage': self.memory_usage,
            'cpu_usage': self.cpu_usage,
            'is_success': self.is_success
        } 