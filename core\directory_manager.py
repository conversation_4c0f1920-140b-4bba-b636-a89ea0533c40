"""
目录管理器 - 统一管理系统目录结构
"""

import os
import json
from pathlib import Path
from typing import Dict, Any


class DirectoryManager:
    """目录管理器"""
    
    def __init__(self, base_dir: str = "."):
        self.base_dir = Path(base_dir)
        self.setup_directories()
    
    def setup_directories(self) -> None:
        """设置目录结构"""
        # 创建主要目录
        directories = {
            'config': self.base_dir / 'config',      # 配置文件目录
            'userdata': self.base_dir / 'userdata',  # 用户数据目录
            'logs': self.base_dir / 'logs',          # 日志目录
            'tasks': self.base_dir / 'tasks',        # 任务目录
        }
        
        for name, path in directories.items():
            path.mkdir(exist_ok=True)
            print(f"📁 确保目录存在: {path}")
        
        # 创建用户数据子目录
        userdata_subdirs = {
            'database': self.get_userdata_dir() / 'database',    # 数据库配置
            'api': self.get_userdata_dir() / 'api',              # API配置
            'files': self.get_userdata_dir() / 'files',          # 文件存储
            'cache': self.get_userdata_dir() / 'cache',          # 缓存目录
        }
        
        for name, path in userdata_subdirs.items():
            path.mkdir(exist_ok=True)
            print(f"📂 确保用户数据子目录存在: {path}")
        
        # 创建说明文件
        self.create_readme_files()
    
    def get_config_dir(self) -> Path:
        """获取配置目录"""
        return self.base_dir / 'config'
    
    def get_userdata_dir(self) -> Path:
        """获取用户数据目录"""
        return self.base_dir / 'userdata'
    
    def get_logs_dir(self) -> Path:
        """获取日志目录"""
        return self.base_dir / 'logs'
    
    def get_tasks_dir(self) -> Path:
        """获取任务目录"""
        return self.base_dir / 'tasks'
    
    def get_config_file(self, filename: str) -> Path:
        """获取配置文件路径"""
        return self.get_config_dir() / filename
    
    def get_userdata_file(self, category: str, filename: str) -> Path:
        """获取用户数据文件路径"""
        return self.get_userdata_dir() / category / filename
    
    def create_readme_files(self) -> None:
        """创建说明文件"""
        # 配置目录说明
        config_readme = self.get_config_dir() / "README.md"
        if not config_readme.exists():
            with open(config_readme, 'w', encoding='utf-8') as f:
                f.write("""# 配置文件目录

此目录包含系统的所有配置文件。

## 文件说明

- `task_configs.json` - 任务配置文件
- `scheduler_config.json` - 调度器配置文件
- `app_settings.json` - 应用程序设置

## 注意事项

- 请勿手动编辑配置文件，除非您知道自己在做什么
- 建议在修改前备份配置文件
- 配置文件使用UTF-8编码
""")
        
        # 用户数据目录说明
        userdata_readme = self.get_userdata_dir() / "README.md"
        if not userdata_readme.exists():
            with open(userdata_readme, 'w', encoding='utf-8') as f:
                f.write("""# 用户数据目录

此目录包含用户自定义的数据和配置。

## 目录结构

```
userdata/
├── database/          # 数据库配置
│   ├── mysql.json     # MySQL配置
│   ├── sqlite.json    # SQLite配置
│   └── redis.json     # Redis配置
├── api/               # API配置
│   ├── endpoints.json # API端点配置
│   └── tokens.json    # API令牌配置
├── files/             # 文件存储
│   ├── uploads/       # 上传文件
│   └── downloads/     # 下载文件
└── cache/             # 缓存目录
    ├── temp/          # 临时文件
    └── data/          # 数据缓存
```

## 使用说明

- 任务可以通过 `get_userdata_file()` 方法访问这些配置
- 所有用户数据都应该存储在此目录下
- 敏感信息请注意加密存储
""")
    
    def create_default_userdata_configs(self) -> None:
        """创建默认的用户数据配置文件"""
        # 数据库配置示例
        db_configs = {
            'mysql': {
                'host': 'localhost',
                'port': 3306,
                'username': '',
                'password': '',
                'database': '',
                'charset': 'utf8mb4'
            },
            'sqlite': {
                'database_path': 'userdata/database/app.db',
                'timeout': 30
            },
            'redis': {
                'host': 'localhost',
                'port': 6379,
                'password': '',
                'db': 0
            }
        }
        
        for db_type, config in db_configs.items():
            config_file = self.get_userdata_file('database', f'{db_type}.json')
            if not config_file.exists():
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                print(f"📄 创建默认配置: {config_file}")
        
        # API配置示例
        api_config = {
            'endpoints': {
                'weather_api': 'https://api.openweathermap.org/data/2.5/weather',
                'news_api': 'https://newsapi.org/v2/top-headlines'
            },
            'rate_limits': {
                'weather_api': 60,  # 每分钟请求数
                'news_api': 100
            }
        }
        
        api_config_file = self.get_userdata_file('api', 'endpoints.json')
        if not api_config_file.exists():
            with open(api_config_file, 'w', encoding='utf-8') as f:
                json.dump(api_config, f, ensure_ascii=False, indent=2)
            print(f"📄 创建API配置: {api_config_file}")
    
    def load_userdata_config(self, category: str, filename: str) -> Dict[str, Any]:
        """加载用户数据配置"""
        config_file = self.get_userdata_file(category, filename)
        
        if not config_file.exists():
            print(f"⚠️ 配置文件不存在: {config_file}")
            return {}
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {config_file} - {e}")
            return {}
    
    def save_userdata_config(self, category: str, filename: str, config: Dict[str, Any]) -> bool:
        """保存用户数据配置"""
        try:
            config_file = self.get_userdata_file(category, filename)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"💾 保存配置成功: {config_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
            return False
    
    def migrate_old_configs(self) -> None:
        """迁移旧的配置文件到新目录"""
        old_configs = [
            ('task_configs.json', 'task_configs.json'),
            ('scheduler_config.json', 'scheduler_config.json'),
        ]
        
        for old_file, new_file in old_configs:
            old_path = self.base_dir / old_file
            new_path = self.get_config_file(new_file)
            
            if old_path.exists() and not new_path.exists():
                try:
                    # 复制文件到新位置
                    import shutil
                    shutil.copy2(old_path, new_path)
                    print(f"📦 迁移配置文件: {old_file} → {new_path}")
                    
                    # 删除旧文件
                    old_path.unlink()
                    print(f"🗑️ 删除旧配置文件: {old_path}")
                    
                except Exception as e:
                    print(f"❌ 迁移配置文件失败: {e}")


if __name__ == "__main__":
    # 测试目录管理器
    manager = DirectoryManager()
    
    print("\n📁 目录结构:")
    print(f"  配置目录: {manager.get_config_dir()}")
    print(f"  用户数据目录: {manager.get_userdata_dir()}")
    print(f"  日志目录: {manager.get_logs_dir()}")
    print(f"  任务目录: {manager.get_tasks_dir()}")
    
    # 创建默认配置
    manager.create_default_userdata_configs()
    
    # 测试配置加载
    db_config = manager.load_userdata_config('database', 'mysql.json')
    print(f"\n📄 MySQL配置: {db_config}")
    
    print("\n✅ 目录管理器测试完成")
