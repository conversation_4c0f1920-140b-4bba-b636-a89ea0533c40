#!/usr/bin/env python3
"""
测试任务状态切换功能
"""

import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from core.config_manager import ConfigManager
from core.task_scanner import TaskScanner
from core.task_scheduler import TaskScheduler
from logs.task_logger import TaskLogger

def status_monitor(config_manager: ConfigManager, task_name: str, duration: int = 30):
    """监控任务状态变化"""
    print(f"🔍 开始监控任务状态: {task_name}")
    
    start_time = time.time()
    last_status = None
    status_changes = []
    
    while time.time() - start_time < duration:
        try:
            config = config_manager.get_task_config(task_name)
            current_status = config.status
            current_time = time.strftime("%H:%M:%S")
            
            if current_status != last_status:
                change_info = f"[{current_time}] 状态变化: {last_status} → {current_status}"
                print(change_info)
                status_changes.append(change_info)
                last_status = current_status
            
            time.sleep(1)  # 每秒检查一次
            
        except Exception as e:
            print(f"❌ 监控状态时出错: {e}")
            break
    
    print(f"\n📊 状态变化总结:")
    for change in status_changes:
        print(f"  {change}")
    
    return status_changes

def test_status_switching():
    """测试状态切换功能"""
    print("🧪 开始测试任务状态切换...")
    
    # 初始化组件
    task_scanner = TaskScanner()
    config_manager = ConfigManager()
    task_logger = TaskLogger()
    
    # 状态回调函数
    def status_callback(message: str):
        current_time = time.strftime("%H:%M:%S")
        print(f"📢 [{current_time}] 调度器状态: {message}")
    
    scheduler = TaskScheduler(task_scanner, config_manager, task_logger, status_callback)
    
    # 选择一个测试任务
    test_task_name = "快速测试任务"
    
    # 显示初始状态
    print(f"\n📋 测试任务: {test_task_name}")
    initial_config = config_manager.get_task_config(test_task_name)
    print(f"初始状态: {initial_config.status}")
    print(f"运行次数: {initial_config.run_count}")
    print(f"下次运行: {initial_config.next_run_time}")
    
    # 启动状态监控线程
    monitor_thread = threading.Thread(
        target=status_monitor, 
        args=(config_manager, test_task_name, 60),
        daemon=True
    )
    monitor_thread.start()
    
    # 启动调度器
    print(f"\n🚀 启动调度器...")
    if scheduler.start():
        print("✅ 调度器启动成功")
        
        # 等待一段时间观察任务执行
        print(f"\n⏳ 等待任务执行 (60秒)...")
        time.sleep(60)
        
        # 停止调度器
        print(f"\n⏹️ 停止调度器...")
        scheduler.stop()
        
    else:
        print("❌ 调度器启动失败")
    
    # 等待监控线程结束
    monitor_thread.join(timeout=5)
    
    # 显示最终状态
    print(f"\n📋 最终状态:")
    final_config = config_manager.get_task_config(test_task_name)
    print(f"最终状态: {final_config.status}")
    print(f"运行次数: {final_config.run_count}")
    print(f"最后运行: {final_config.last_run_time}")
    print(f"下次运行: {final_config.next_run_time}")
    
    print(f"\n✅ 状态切换测试完成")

if __name__ == "__main__":
    test_status_switching()
