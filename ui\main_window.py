"""
主窗口UI界面
现代化、美观、简约的自动化任务调度管理界面
"""

import sys
from pathlib import Path
from typing import List, Optional
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QPushButton,
    QLabel, QLineEdit, QComboBox, QTextEdit, QSplitter,
    QGroupBox, QGridLayout, QStatusBar,
    QHeaderView, QFrame, QMessageBox, QDialog
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.task_scanner import TaskScanner, TaskInfo


class ModernButton(QPushButton):
    """现代化按钮样式"""
    
    def __init__(self, text: str, button_type: str = "primary"):
        super().__init__(text)
        self.set_button_style(button_type)
    
    def set_button_style(self, button_type: str) -> None:
        """设置按钮样式"""
        base_style = """
            QPushButton {
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: 600;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: rgba(0, 0, 0, 0.1);
            }
            QPushButton:pressed {
                background-color: rgba(0, 0, 0, 0.2);
            }
        """
        
        if button_type == "primary":
            style = base_style + """
                QPushButton {
                    background-color: #3b82f6;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #2563eb;
                }
                QPushButton:pressed {
                    background-color: #1d4ed8;
                }
            """
        elif button_type == "success":
            style = base_style + """
                QPushButton {
                    background-color: #10b981;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #059669;
                }
                QPushButton:pressed {
                    background-color: #047857;
                }
            """
        elif button_type == "danger":
            style = base_style + """
                QPushButton {
                    background-color: #ef4444;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #dc2626;
                }
                QPushButton:pressed {
                    background-color: #b91c1c;
                }
            """
        elif button_type == "secondary":
            style = base_style + """
                QPushButton {
                    background-color: #6b7280;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #4b5563;
                }
                QPushButton:pressed {
                    background-color: #374151;
                }
            """
        
        self.setStyleSheet(style)


class TaskTableWidget(QTableWidget):
    """任务列表表格"""

    def __init__(self):
        super().__init__()
        self.tasks_data: List[TaskInfo] = []
        self.setup_table()

    def setup_table(self) -> None:
        """设置表格"""
        # 设置列
        headers = ["任务名称", "描述", "分类", "作者", "版本", "状态", "操作"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # 设置表格样式
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e5e7eb;
                background-color: white;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #f3f4f6;
            }
            QTableWidget::item:selected {
                background-color: #eff6ff;
                color: #1e40af;
            }
            QHeaderView::section {
                background-color: #f9fafb;
                padding: 12px;
                border: none;
                border-bottom: 2px solid #e5e7eb;
                font-weight: 600;
                color: #374151;
            }
        """)
        
        # 设置表格属性
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.horizontalHeader().setStretchLastSection(True)
        self.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

    def load_tasks(self, tasks: List[TaskInfo]) -> None:
        """加载任务数据到表格"""
        self.tasks_data = tasks
        self.setRowCount(len(tasks))

        for row, task in enumerate(tasks):
            # 任务名称
            self.setItem(row, 0, QTableWidgetItem(task.name))

            # 描述
            self.setItem(row, 1, QTableWidgetItem(task.description))

            # 分类
            self.setItem(row, 2, QTableWidgetItem(task.category))

            # 作者
            self.setItem(row, 3, QTableWidgetItem(task.author))

            # 版本
            self.setItem(row, 4, QTableWidgetItem(task.version))

            # 状态
            status = "启用" if task.is_enabled else "禁用"
            self.setItem(row, 5, QTableWidgetItem(status))

            # 操作按钮
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(5, 2, 5, 2)

            run_btn = ModernButton("▶️ 运行", "success")
            run_btn.setMaximumHeight(30)
            edit_btn = ModernButton("📝 编辑", "primary")
            edit_btn.setMaximumHeight(30)

            action_layout.addWidget(run_btn)
            action_layout.addWidget(edit_btn)
            action_layout.addStretch()

            self.setCellWidget(row, 6, action_widget)

    def get_selected_task(self) -> Optional[TaskInfo]:
        """获取当前选中的任务"""
        current_row = self.currentRow()
        if 0 <= current_row < len(self.tasks_data):
            return self.tasks_data[current_row]
        return None


class LogWidget(QTextEdit):
    """日志显示组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_log_widget()
    
    def setup_log_widget(self) -> None:
        """设置日志组件"""
        self.setReadOnly(True)
        self.setStyleSheet("""
            QTextEdit {
                background-color: #1f2937;
                color: #f9fafb;
                border: 1px solid #374151;
                border-radius: 8px;
                padding: 12px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        
        # 添加示例日志
        self.append("[2024-07-02 12:00:00] 系统启动")
        self.append("[2024-07-02 12:00:01] 加载任务配置")
        self.append("[2024-07-02 12:00:02] 调度器初始化完成")


class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.task_scanner = TaskScanner()
        self.current_tasks: List[TaskInfo] = []
        self.setup_ui()
        self.setup_connections()
        self.load_tasks()
    
    def setup_ui(self) -> None:
        """设置UI界面"""
        self.setWindowTitle("自动化任务调度管理系统")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置应用样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8fafc;
            }
            QTabWidget::pane {
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                background-color: white;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #f3f4f6;
                padding: 12px 24px;
                margin-right: 4px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                color: #6b7280;
                font-weight: 500;
            }
            QTabBar::tab:selected {
                background-color: white;
                color: #1f2937;
                border-bottom: 2px solid #3b82f6;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建标题区域
        self.create_header(main_layout)
        
        # 创建主要内容区域
        self.create_main_content(main_layout)

        # 创建状态栏
        self.create_status_bar()
    
    def create_header(self, parent_layout: QVBoxLayout) -> None:
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # 标题
        title_label = QLabel("🤖 自动化任务调度管理系统")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: 700;
                color: #1f2937;
                background: none;
                border: none;
            }
        """)
        
        # 快速操作按钮
        quick_actions = QHBoxLayout()

        self.btn_refresh = ModernButton("� 刷新任务", "primary")
        self.btn_start_all = ModernButton("▶️ 启动所有", "success")
        self.btn_stop_all = ModernButton("⏹️ 停止所有", "danger")
        self.btn_settings = ModernButton("⚙️ 设置", "secondary")

        quick_actions.addWidget(self.btn_refresh)
        quick_actions.addWidget(self.btn_start_all)
        quick_actions.addWidget(self.btn_stop_all)
        quick_actions.addWidget(self.btn_settings)
        quick_actions.addStretch()
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addLayout(quick_actions)
        
        parent_layout.addWidget(header_frame)
    
    def create_main_content(self, parent_layout: QVBoxLayout) -> None:
        """创建主要内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：任务列表
        left_widget = self.create_task_list_panel()

        # 右侧：日志监控
        right_widget = self.create_log_panel()

        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([800, 600])  # 左侧800px，右侧600px

        parent_layout.addWidget(splitter)
    
    def create_task_list_panel(self) -> QWidget:
        """创建任务列表面板"""
        task_widget = QWidget()
        task_layout = QVBoxLayout(task_widget)
        task_layout.setContentsMargins(20, 20, 20, 20)

        # 任务列表标题和搜索
        list_header = QHBoxLayout()
        list_title = QLabel("📋 任务列表")
        list_title.setStyleSheet("font-size: 18px; font-weight: 600; color: #1f2937;")

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 搜索任务...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                font-size: 14px;
                max-width: 300px;
            }
            QLineEdit:focus {
                border-color: #3b82f6;
            }
        """)

        list_header.addWidget(list_title)
        list_header.addStretch()
        list_header.addWidget(self.search_input)

        # 任务表格
        self.task_table = TaskTableWidget()

        task_layout.addLayout(list_header)
        task_layout.addWidget(self.task_table)

        return task_widget
    

    
    def create_log_panel(self) -> QWidget:
        """创建日志监控面板"""
        log_widget = QWidget()
        log_layout = QVBoxLayout(log_widget)
        log_layout.setContentsMargins(20, 20, 20, 20)

        # 日志标题和控制
        log_header = QHBoxLayout()
        log_title = QLabel("📊 系统日志")
        log_title.setStyleSheet("font-size: 18px; font-weight: 600; color: #1f2937;")

        # 日志级别过滤
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["全部", "INFO", "WARNING", "ERROR", "DEBUG"])
        self.log_level_combo.setStyleSheet("""
            QComboBox {
                padding: 6px 12px;
                border: 2px solid #e5e7eb;
                border-radius: 6px;
                font-size: 12px;
                min-width: 80px;
            }
        """)

        self.btn_clear_log = ModernButton("🗑️ 清空", "secondary")
        self.btn_clear_log.setMaximumHeight(32)
        self.btn_export_log = ModernButton("📤 导出", "primary")
        self.btn_export_log.setMaximumHeight(32)

        log_header.addWidget(log_title)
        log_header.addStretch()
        log_header.addWidget(QLabel("级别:"))
        log_header.addWidget(self.log_level_combo)
        log_header.addWidget(self.btn_clear_log)
        log_header.addWidget(self.btn_export_log)

        # 日志显示区域
        self.log_display = LogWidget()

        log_layout.addLayout(log_header)
        log_layout.addWidget(self.log_display)

        return log_widget
    

    
    def create_status_bar(self) -> None:
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 状态信息
        self.status_bar.showMessage("系统就绪")
        
        # 添加永久部件
        self.task_count_label = QLabel("任务总数: 0")
        self.running_count_label = QLabel("运行中: 0")
        
        self.status_bar.addPermanentWidget(self.task_count_label)
        self.status_bar.addPermanentWidget(self.running_count_label)
    
    def setup_connections(self) -> None:
        """设置信号连接"""
        # 快速操作按钮
        self.btn_refresh.clicked.connect(self.refresh_tasks)
        self.btn_settings.clicked.connect(self.show_settings_dialog)

        # 日志控制按钮
        self.btn_clear_log.clicked.connect(self.clear_log)
        self.btn_export_log.clicked.connect(self.export_log)

        # 任务表格选择事件
        self.task_table.itemSelectionChanged.connect(self.on_task_selected)

        # 搜索框
        self.search_input.textChanged.connect(self.filter_tasks)

        # 日志级别过滤
        self.log_level_combo.currentTextChanged.connect(self.filter_log)

    def load_tasks(self) -> None:
        """加载任务列表"""
        try:
            self.current_tasks = self.task_scanner.scan_tasks()
            self.task_table.load_tasks(self.current_tasks)
            self.update_status_bar()

            if self.current_tasks:
                self.log_display.append(f"[{self.get_current_time()}] ✅ 成功加载 {len(self.current_tasks)} 个任务")
            else:
                self.log_display.append(f"[{self.get_current_time()}] ⚠️ 未发现任何任务文件")

        except Exception as e:
            self.log_display.append(f"[{self.get_current_time()}] ❌ 加载任务失败: {e}")

    def refresh_tasks(self) -> None:
        """刷新任务列表"""
        self.log_display.append(f"[{self.get_current_time()}] 🔄 刷新任务列表...")
        self.load_tasks()

    def filter_tasks(self, text: str) -> None:
        """过滤任务列表"""
        if not text:
            self.task_table.load_tasks(self.current_tasks)
            return

        filtered_tasks = [
            task for task in self.current_tasks
            if text.lower() in task.name.lower() or
               text.lower() in task.description.lower() or
               text.lower() in task.category.lower()
        ]
        self.task_table.load_tasks(filtered_tasks)

    def on_task_selected(self) -> None:
        """任务选择事件"""
        selected_task = self.task_table.get_selected_task()
        if selected_task:
            self.log_display.append(
                f"[{self.get_current_time()}] 📋 选中任务: {selected_task.name}"
            )

    def update_status_bar(self) -> None:
        """更新状态栏"""
        total_tasks = len(self.current_tasks)
        enabled_tasks = sum(1 for task in self.current_tasks if task.is_enabled)

        self.task_count_label.setText(f"任务总数: {total_tasks}")
        self.running_count_label.setText(f"启用任务: {enabled_tasks}")

        if total_tasks > 0:
            self.status_bar.showMessage(f"就绪 - 发现 {total_tasks} 个任务")
        else:
            self.status_bar.showMessage("就绪 - 未发现任务文件")

    def show_settings_dialog(self) -> None:
        """显示设置对话框"""
        dialog = SettingsDialog(self)
        if dialog.exec() == QMessageBox.StandardButton.Ok:
            self.log_display.append(f"[{self.get_current_time()}] ⚙️ 设置已更新")

    def clear_log(self) -> None:
        """清空日志"""
        self.log_display.clear()
        self.log_display.append(f"[{self.get_current_time()}] 🗑️ 日志已清空")

    def export_log(self) -> None:
        """导出日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"logs/system_log_{timestamp}.txt"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_display.toPlainText())
            self.log_display.append(f"[{self.get_current_time()}] 📤 日志已导出到: {filename}")
        except Exception as e:
            self.log_display.append(f"[{self.get_current_time()}] ❌ 导出失败: {e}")

    def filter_log(self, level: str) -> None:
        """过滤日志级别"""
        # 这里可以实现日志级别过滤逻辑
        self.log_display.append(f"[{self.get_current_time()}] 🔍 日志过滤: {level}")

    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("自动化任务调度管理系统")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
