"""
任务日志管理器 - 为每个任务创建独立的日志文件夹和日志文件
"""

import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List


class TaskLogger:
    """任务日志管理器"""
    
    def __init__(self, logs_dir: str = "logs"):
        self.logs_dir = Path(logs_dir)
        self.logs_dir.mkdir(exist_ok=True)
        self.create_readme()
    
    def create_readme(self) -> None:
        """创建日志目录说明文件"""
        readme_file = self.logs_dir / "README.md"
        if not readme_file.exists():
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write("""# 任务执行日志

此目录包含所有任务的执行日志。

## 目录结构

```
logs/
├── 任务名称1/
│   ├── 2025-07-02_14-30-15.log
│   ├── 2025-07-02_14-35-20.log
│   └── ...
├── 任务名称2/
│   ├── 2025-07-02_14-32-10.log
│   └── ...
└── README.md
```

## 日志文件命名规则
- 格式: YYYY-MM-DD_HH-MM-SS.log
- 每次任务执行都会创建一个新的日志文件
""")
    
    def get_task_log_dir(self, task_name: str) -> Path:
        """获取任务的日志目录"""
        # 清理任务名称，移除不合法的文件名字符
        safe_task_name = self.sanitize_filename(task_name)
        task_dir = self.logs_dir / safe_task_name
        task_dir.mkdir(exist_ok=True)
        return task_dir
    
    def sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除不合法字符"""
        # 移除或替换不合法的文件名字符
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # 移除首尾空格和点
        filename = filename.strip(' .')
        
        # 如果文件名为空，使用默认名称
        if not filename:
            filename = "unnamed_task"
        
        return filename
    
    def create_log_file(self, task_name: str) -> Path:
        """为任务创建新的日志文件"""
        task_dir = self.get_task_log_dir(task_name)
        
        # 生成日志文件名：YYYY-MM-DD_HH-MM-SS.log
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        log_file = task_dir / f"{timestamp}.log"
        
        # 创建日志文件并写入简洁的头部信息
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"🚀 {task_name} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 50 + "\n")
        
        return log_file
    
    def write_log(self, log_file: Path, level: str, message: str) -> None:
        """写入日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}\n"
        
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception as e:
            print(f"❌ 写入日志失败: {e}")
    
    def finish_log(self, log_file: Path, success: bool = True, error_msg: str = "") -> None:
        """完成日志记录"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write("=" * 50 + "\n")
                status_icon = "✅" if success else "❌"
                f.write(f"{status_icon} 完成 - {timestamp}")
                if not success and error_msg:
                    f.write(f" | 错误: {error_msg}")
                f.write("\n")
        except Exception as e:
            print(f"❌ 完成日志记录失败: {e}")
    
    def get_task_logs(self, task_name: str) -> List[Dict]:
        """获取指定任务的所有日志文件"""
        task_dir = self.get_task_log_dir(task_name)
        logs = []
        
        if not task_dir.exists():
            return logs
        
        try:
            for log_file in task_dir.glob("*.log"):
                stat = log_file.stat()
                logs.append({
                    'file_name': log_file.name,
                    'file_path': str(log_file),
                    'size': stat.st_size,
                    'created_time': datetime.fromtimestamp(stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S'),
                    'modified_time': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
            
            # 按创建时间排序（最新的在前）
            logs.sort(key=lambda x: x['created_time'], reverse=True)
            
        except Exception as e:
            print(f"❌ 获取任务日志失败: {e}")
        
        return logs
    
    def get_all_task_logs(self) -> Dict[str, List[Dict]]:
        """获取所有任务的日志文件"""
        all_logs = {}
        
        try:
            for task_dir in self.logs_dir.iterdir():
                if task_dir.is_dir() and task_dir.name != "__pycache__":
                    task_name = task_dir.name
                    all_logs[task_name] = self.get_task_logs(task_name)
        except Exception as e:
            print(f"❌ 获取所有任务日志失败: {e}")
        
        return all_logs
    
    def clean_old_logs(self, task_name: str, keep_count: int = 10) -> None:
        """清理旧的日志文件，只保留最新的几个"""
        logs = self.get_task_logs(task_name)
        
        if len(logs) <= keep_count:
            return
        
        # 删除多余的旧日志文件
        logs_to_delete = logs[keep_count:]
        
        for log_info in logs_to_delete:
            try:
                log_file = Path(log_info['file_path'])
                if log_file.exists():
                    log_file.unlink()
                    print(f"🗑️ 删除旧日志: {log_file.name}")
            except Exception as e:
                print(f"❌ 删除日志文件失败: {e}")
    
    def read_log_content(self, log_file_path: str) -> str:
        """读取日志文件内容"""
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            return f"❌ 读取日志文件失败: {e}"
