# 🤖 自动化任务调度管理系统

一个现代化、美观、功能完整的Python自动化任务调度管理系统，支持可视化界面和命令行操作。

## ✨ 特性

- 🎨 **现代化UI界面** - 基于PyQt6的美观简约界面
- 📋 **任务管理** - 支持任务的增删改查、状态管理
- ⏰ **灵活调度** - 支持多种执行频率设置
- 📊 **实时监控** - 任务状态、日志实时显示
- 🔧 **模块化设计** - 共享API类库，任务独立可复用
- 🚀 **异步执行** - 任务并发执行，互不干扰
- 📝 **完整日志** - 详细的执行日志和错误追踪

## 📁 项目结构

```
hugment_automated_task_system_shike/
├── core/                   # 核心调度台逻辑
│   └── __init__.py
├── ui/                     # PyQt6 UI界面
│   ├── __init__.py
│   └── main_window.py      # 主窗口界面
├── data/                   # 数据文件存储
├── logs/                   # 日志文件
├── shared/                 # 共享类库和API封装
│   └── __init__.py
├── tasks/                  # 任务模块
│   └── __init__.py
├── config.json            # 系统配置文件
├── main.py                # 主启动文件
├── requirements.txt       # 依赖包列表
└── README.md              # 项目说明
```

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- PyQt6

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 启动系统

```bash
# GUI模式 (默认)
python main.py

# 或者明确指定GUI模式
python main.py --gui

# 命令行模式 (开发中)
python main.py --cli
```

## 🎯 功能说明

### 任务管理
- ➕ **添加任务** - 创建新的自动化任务
- 📝 **编辑任务** - 修改任务配置和参数
- ▶️ **执行控制** - 启动、停止、暂停任务
- 📊 **状态监控** - 实时查看任务执行状态

### 调度功能
- ⏰ **多种频率** - 支持分钟、小时、天、周、月级调度
- 🔄 **自动重试** - 任务失败自动重试机制
- 🚦 **并发控制** - 限制同时执行的任务数量

### 日志系统
- 📝 **详细记录** - 记录任务执行的详细信息
- 🔍 **日志过滤** - 按级别过滤日志信息
- 📤 **日志导出** - 支持日志文件导出

## 🛠️ 开发指南

### 添加新任务

1. 在 `tasks/` 目录下创建新的任务文件
2. 继承基础任务类，实现执行逻辑
3. 在UI中添加任务配置

### 添加共享API

1. 在 `shared/` 目录下创建API封装类
2. 遵循统一的接口规范
3. 在任务中导入使用

## 📋 TODO

- [ ] 完善核心调度逻辑
- [ ] 实现任务持久化存储
- [ ] 添加更多任务模板
- [ ] 完善错误处理机制
- [ ] 添加任务依赖关系
- [ ] 实现分布式调度

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
