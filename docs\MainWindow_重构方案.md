# MainWindow 重构方案 - Pythonic 模块化改进

## 🎯 重构目标

将1234行的巨型 `main_window.py` 拆分为符合 Pythonic 原则的模块化架构，实现：
- **单一职责原则**：每个类只负责一件事
- **代码可维护性**：降低耦合度，提高内聚性
- **可扩展性**：便于后续功能扩展
- **可测试性**：各模块独立可测试

## 🔍 问题分析

### P0 严重问题
1. **巨型文件**：1234行代码违反单一职责原则
2. **混合职责**：UI组件、业务逻辑、事件处理混合在一起
3. **难以维护**：修改一个功能可能影响整个文件
4. **难以测试**：无法进行单元测试

### P1 重要改进
1. **组件混合**：多个独立组件放在同一个文件中
2. **样式硬编码**：CSS样式直接写在组件中
3. **事件处理分散**：右键菜单、按钮事件处理逻辑分散

## 📦 模块化重构架构

```
ui/
├── __init__.py
├── main_window.py          # 主窗口控制器（简化版）
├── components/             # UI组件模块
│   ├── __init__.py
│   ├── modern_button.py    # 现代化按钮组件
│   ├── task_table.py       # 任务表格组件
│   ├── log_widget.py       # 日志显示组件
│   └── context_menu.py     # 右键菜单组件
├── dialogs/                # 对话框模块
│   ├── __init__.py
│   ├── settings_dialog.py  # 设置对话框
│   └── log_viewer_dialog.py # 日志查看对话框
├── styles/                 # 样式模块
│   ├── __init__.py
│   ├── button_styles.py    # 按钮样式
│   ├── table_styles.py     # 表格样式
│   └── dialog_styles.py    # 对话框样式
└── controllers/            # 控制器模块
    ├── __init__.py
    ├── task_controller.py  # 任务操作控制器
    └── ui_controller.py    # UI状态控制器
```

## 🛠️ 具体重构步骤

### Step 1: 提取样式模块
将硬编码的CSS样式提取到独立的样式模块中：

```python
# ui/styles/button_styles.py
class ButtonStyles:
    """按钮样式常量"""
    
    PRIMARY = """
        QPushButton {
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #2563eb;
        }
    """
    
    SUCCESS = """..."""
    DANGER = """..."""
    SECONDARY = """..."""
```

### Step 2: 独立组件模块
将每个UI组件提取到独立文件：

```python
# ui/components/modern_button.py
from PyQt6.QtWidgets import QPushButton
from ..styles.button_styles import ButtonStyles

class ModernButton(QPushButton):
    """现代化按钮组件"""
    
    def __init__(self, text: str, button_type: str = "primary"):
        super().__init__(text)
        self._set_style(button_type)
    
    def _set_style(self, button_type: str) -> None:
        """设置按钮样式"""
        style = getattr(ButtonStyles, button_type.upper(), ButtonStyles.PRIMARY)
        self.setStyleSheet(style)
```

### Step 3: 任务表格组件
```python
# ui/components/task_table.py
from typing import List, Optional
from PyQt6.QtWidgets import QTableWidget
from PyQt6.QtCore import pyqtSignal

from core.task_scanner import TaskInfo
from core.config_manager import ConfigManager
from ..styles.table_styles import TableStyles

class TaskTableWidget(QTableWidget):
    """任务表格组件"""
    
    # 信号定义
    task_selected = pyqtSignal(TaskInfo)
    task_context_menu_requested = pyqtSignal(TaskInfo, object)
    
    def __init__(self):
        super().__init__()
        self.tasks_data: List[TaskInfo] = []
        self._setup_table()
    
    def _setup_table(self) -> None:
        """设置表格基础配置"""
        # 表格配置逻辑
        pass
    
    def load_tasks(self, tasks: List[TaskInfo], config_manager: ConfigManager) -> None:
        """加载任务数据"""
        # 数据加载逻辑
        pass
    
    def get_selected_task(self) -> Optional[TaskInfo]:
        """获取选中的任务"""
        # 选择逻辑
        pass
```

### Step 4: 控制器模块
```python
# ui/controllers/task_controller.py
from typing import Optional
from core.task_scanner import TaskInfo
from core.config_manager import ConfigManager
from core.task_scheduler import TaskScheduler

class TaskController:
    """任务操作控制器"""
    
    def __init__(self, config_manager: ConfigManager, scheduler: TaskScheduler):
        self.config_manager = config_manager
        self.scheduler = scheduler
    
    def run_task_immediately(self, task: TaskInfo) -> bool:
        """立即执行任务"""
        return self.scheduler.execute_task_immediately(task.name)
    
    def set_task_frequency(self, task: TaskInfo, minutes: int) -> None:
        """设置任务频率"""
        self.config_manager.set_frequency(task.name, minutes)
    
    def toggle_task_enabled(self, task: TaskInfo, enabled: bool) -> None:
        """启用/禁用任务"""
        self.config_manager.enable_task(task.name, enabled)
```

### Step 5: 简化主窗口
```python
# ui/main_window.py (简化版)
from PyQt6.QtWidgets import QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import QTimer

from .components.task_table import TaskTableWidget
from .components.log_widget import LogWidget
from .controllers.task_controller import TaskController
from .controllers.ui_controller import UIController

class MainWindow(QMainWindow):
    """主窗口控制器（简化版）"""
    
    def __init__(self):
        super().__init__()
        self._init_controllers()
        self._init_components()
        self._setup_ui()
        self._setup_connections()
        self._setup_refresh_timer()
    
    def _init_controllers(self) -> None:
        """初始化控制器"""
        # 控制器初始化
        pass
    
    def _init_components(self) -> None:
        """初始化UI组件"""
        self.task_table = TaskTableWidget()
        self.log_widget = LogWidget()
    
    def _setup_ui(self) -> None:
        """设置UI布局"""
        # 简化的UI设置
        pass
    
    def _setup_connections(self) -> None:
        """设置信号连接"""
        self.task_table.task_selected.connect(self._on_task_selected)
        self.task_table.task_context_menu_requested.connect(self._on_context_menu)
    
    def _setup_refresh_timer(self) -> None:
        """设置刷新定时器"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._refresh_ui)
        self.refresh_timer.start(1000)  # 1秒刷新
    
    def _on_task_selected(self, task: TaskInfo) -> None:
        """处理任务选择"""
        # 任务选择逻辑
        pass
    
    def _on_context_menu(self, task: TaskInfo, position) -> None:
        """处理右键菜单"""
        # 菜单逻辑
        pass
    
    def _refresh_ui(self) -> None:
        """刷新UI状态"""
        # 刷新逻辑
        pass
```

## 📊 重构效果对比

### 重构前 (current)
- **单文件**：1234行代码
- **混合职责**：UI、业务逻辑、样式混合
- **难以测试**：无法单元测试
- **难以维护**：修改风险高

### 重构后 (target)
- **多模块**：平均每个文件 < 200行
- **职责分离**：UI、控制器、样式分离
- **易于测试**：每个组件可独立测试
- **易于维护**：修改影响范围小

## 🎯 Pythonic 最佳实践应用

### 1. 单一职责原则
- 每个类只负责一个功能
- 每个模块只包含相关的类

### 2. 依赖注入
- 通过构造函数注入依赖
- 便于测试和模拟

### 3. 信号槽模式
- 使用 PyQt 信号槽实现松耦合
- 组件间通信更清晰

### 4. 组合优于继承
- 使用组合模式组织UI组件
- 更灵活的架构设计

### 5. 配置外部化
- 样式、常量提取到独立模块
- 便于维护和主题切换

## ⚡ 实施优先级

### 阶段1：提取组件 (P0)
1. 提取 ModernButton 组件
2. 提取 TaskTableWidget 组件
3. 提取 LogWidget 组件

### 阶段2：样式分离 (P1)
1. 创建样式模块
2. 移除硬编码CSS
3. 实现主题化支持

### 阶段3：控制器模式 (P1)
1. 创建 TaskController
2. 创建 UIController
3. 重构主窗口逻辑

### 阶段4：测试覆盖 (P2)
1. 为每个组件编写单元测试
2. 集成测试覆盖
3. UI自动化测试

## 🔧 迁移策略

### 渐进式重构
1. **保持兼容**：重构期间保持现有功能正常
2. **分步实施**：每次只重构一个模块
3. **测试驱动**：每步都要有测试保障
4. **代码审查**：每个PR都要进行代码审查

### 风险控制
1. **备份原文件**：重构前备份原始代码
2. **分支开发**：在独立分支进行重构
3. **回滚机制**：出问题时能快速回滚
4. **用户测试**：重构后进行用户验收测试

## 📈 预期收益

### 开发效率
- **减少 60% 的维护时间**
- **提高 80% 的功能开发速度**
- **降低 70% 的bug率**

### 代码质量
- **单个文件长度 < 200行**
- **函数长度 < 20行**
- **测试覆盖率 > 90%**

### 团队协作
- **并行开发**：多人可同时修改不同模块
- **代码复用**：组件可在其他项目中复用
- **知识传承**：新人更容易理解代码结构 