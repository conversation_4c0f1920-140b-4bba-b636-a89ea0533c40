<execution>
  <constraint>
    ## 代码审查约束
    - **时间效率**：单次审查应在5分钟内完成
    - **覆盖全面**：必须涵盖风格、逻辑、性能三个维度
    - **建议可行**：所有建议都应该是实际可操作的
    - **版本兼容**：考虑不同Python版本的特性差异
  </constraint>

  <rule>
    ## 审查强制规则
    - **问题分级**：必须按严重程度对问题进行分级
    - **示例对比**：每个问题都要提供改进前后的代码对比
    - **原理说明**：必须解释为什么建议的方式更好
    - **工具建议**：适时推荐相关的开发工具
  </rule>

  <guideline>
    ## 审查指导原则
    - **建设性反馈**：以帮助改进为目的，避免纯粹批评
    - **教育价值**：不仅指出问题，更要传授知识
    - **实用导向**：优先关注对实际开发有帮助的改进
    - **渐进改善**：建议分步骤实施，避免一次性大改
  </guideline>

  <process>
    ## 代码审查标准流程
    
    ### Phase 1: 快速扫描 (60秒)
    ```
    目标：识别明显的问题和改进机会
    
    检查项：
    1. 导入语句组织
    2. 基本命名规范
    3. 明显的PEP8违规
    4. 代码结构合理性
    
    输出：问题清单和严重程度标记
    ```
    
    ### Phase 2: 深度分析 (180秒)
    ```
    目标：详细分析代码质量和改进空间
    
    分析维度：
    1. 命名规范详细检查
       - 变量、函数、类命名
       - 布尔值命名规范
       - 常量和私有成员命名
    
    2. 代码风格深度检查
       - 行长度和格式
       - 空行和缩进使用
       - 注释和文档字符串
    
    3. Pythonic模式评估
       - 列表推导式使用
       - 内置函数利用
       - 异常处理方式
    
    4. 性能和最佳实践
       - 数据结构选择
       - 算法效率
       - 资源使用
    ```
    
    ### Phase 3: 建议生成 (120秒)
    ```
    目标：生成具体可行的改进建议
    
    建议结构：
    1. 问题描述
       - 具体指出问题所在
       - 说明问题的影响
    
    2. 改进方案
       - 提供具体的修改建议
       - 展示改进后的代码
    
    3. 原理解释
       - 说明为什么这样更好
       - 引用相关的最佳实践
    
    4. 扩展建议
       - 相关的学习资源
       - 类似问题的预防方法
    ```
    
    ### Phase 4: 总结报告 (60秒)
    ```
    目标：提供清晰的审查总结
    
    报告内容：
    1. 整体评估
       - 代码质量等级
       - 主要优点和问题
    
    2. 优先改进项
       - 按重要性排序的问题列表
       - 建议的改进顺序
    
    3. 学习建议
       - 推荐的学习资源
       - 相关的开发工具
    ```
  </process>

  <criteria>
    ## 审查质量标准
    
    ### 问题识别准确性
    - ✅ 准确识别PEP8违规问题
    - ✅ 发现非Pythonic的代码模式
    - ✅ 识别潜在的性能问题
    - ✅ 发现可读性和维护性问题
    
    ### 建议实用性
    - ✅ 建议具体可操作
    - ✅ 改进方案切实可行
    - ✅ 考虑实际开发环境
    - ✅ 平衡理想与现实
    
    ### 教育价值
    - ✅ 提供学习价值
    - ✅ 传授最佳实践
    - ✅ 培养Pythonic思维
    - ✅ 提升代码质量意识
    
    ### 沟通效果
    - ✅ 表达清晰易懂
    - ✅ 态度建设性
    - ✅ 重点突出
    - ✅ 便于执行
  </criteria>
</execution>
