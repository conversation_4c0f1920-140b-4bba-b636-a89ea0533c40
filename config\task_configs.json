{"示例任务": {"name": "示例任务", "status": "等待中", "frequency_minutes": 30, "next_run_time": "2025-07-02 21:44:32", "run_count": 0, "last_run_time": "", "is_enabled": true}, "数据采集": {"name": "数据采集", "status": "执行中", "frequency_minutes": 120, "next_run_time": "2025-07-02 21:44:32", "run_count": 0, "last_run_time": "", "is_enabled": true}, "文件整理助手": {"name": "文件整理助手", "status": "等待中", "frequency_minutes": 1, "next_run_time": "2025-07-02 21:56:20", "run_count": 1, "last_run_time": "2025-07-02 21:29:45", "is_enabled": true}, "示例数据处理任务": {"name": "示例数据处理任务", "status": "等待中", "frequency_minutes": 66, "next_run_time": "2025-07-02 22:09:57", "run_count": 0, "last_run_time": "", "is_enabled": true}, "网页数据采集任务": {"name": "网页数据采集任务", "status": "等待中", "frequency_minutes": 60, "next_run_time": "2025-07-02 21:44:41", "run_count": 1, "last_run_time": "2025-07-02 21:17:26", "is_enabled": false}, "全局配置演示": {"name": "全局配置演示", "status": "等待中", "frequency_minutes": 60, "next_run_time": "2025-07-02 22:45:03", "run_count": 0, "last_run_time": "", "is_enabled": true}, "长时间任务": {"name": "长时间任务", "status": "等待中", "frequency_minutes": 1, "next_run_time": "2025-07-02 22:02:25", "run_count": 1, "last_run_time": "2025-07-02 22:01:25", "is_enabled": true}, "中等延迟任务": {"name": "中等延迟任务", "status": "等待中", "frequency_minutes": 1, "next_run_time": "2025-07-02 22:02:22", "run_count": 1, "last_run_time": "2025-07-02 22:01:22", "is_enabled": true}, "快速测试任务": {"name": "快速测试任务", "status": "等待中", "frequency_minutes": 1, "next_run_time": "2025-07-02 22:02:18", "run_count": 1, "last_run_time": "2025-07-02 22:01:18", "is_enabled": true}, "状态监控任务": {"name": "状态监控任务", "status": "等待中", "frequency_minutes": 1, "next_run_time": "2025-07-02 22:02:17", "run_count": 1, "last_run_time": "2025-07-02 22:01:17", "is_enabled": true}}