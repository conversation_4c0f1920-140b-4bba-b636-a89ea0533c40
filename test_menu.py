"""
测试右键菜单效果的简单脚本
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QMenu
from PyQt6.QtCore import Qt

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("右键菜单测试")
        self.setGeometry(100, 100, 400, 300)
        
        label = QLabel("右键点击这里查看菜单效果", self)
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("font-size: 16px; padding: 20px;")
        self.setCentralWidget(label)
        
        # 设置右键菜单
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu(self)
        
        # 设置菜单样式 - 适中大小的外观
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #d1d5db;
                border-radius: 8px;
                padding: 6px 0px;
                font-size: 13px;
                font-weight: 500;
                min-width: 160px;
                color: #374151;
            }
            QMenu::item {
                background-color: transparent;
                padding: 8px 16px;
                margin: 1px 6px;
                border-radius: 6px;
                color: #374151;
                font-weight: 500;
                border: none;
            }
            QMenu::item:selected {
                background-color: #f3f4f6;
                color: #1f2937;
            }
            QMenu::item:pressed {
                background-color: #e5e7eb;
                color: #1f2937;
            }
            QMenu::separator {
                height: 1px;
                background-color: #e5e7eb;
                margin: 4px 12px;
                border: none;
            }
        """)
        
        # 添加菜单项
        run_action = menu.addAction("▶️ 立即执行")
        run_action.triggered.connect(lambda: print("立即执行"))

        menu.addSeparator()

        freq_action = menu.addAction("⏰ 设置频率")
        freq_action.triggered.connect(lambda: print("设置频率"))

        custom_action = menu.addAction("🔧 自定义语法")
        custom_action.triggered.connect(lambda: print("自定义语法"))

        menu.addSeparator()

        disable_action = menu.addAction("⏸️ 禁用任务")
        disable_action.triggered.connect(lambda: print("禁用任务"))
        
        menu.exec(self.mapToGlobal(position))

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec())
