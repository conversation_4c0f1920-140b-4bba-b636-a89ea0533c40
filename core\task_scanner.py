"""
任务扫描器 - 自动识别tasks文件夹中的Python任务文件
"""

import os
import sys
import importlib.util
import inspect
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class TaskInfo:
    """任务信息数据类"""
    name: str
    file_path: str
    description: str
    author: str
    version: str
    category: str
    is_enabled: bool = True
    last_modified: float = 0.0


@dataclass
class TaskConfig:
    """任务配置数据类"""
    name: str
    status: str = "等待中"  # 执行中、等待中、已禁用
    frequency_minutes: int = 60  # 执行频率（分钟）
    next_run_time: str = ""  # 下次运行时间
    run_count: int = 0  # 已运行次数
    last_run_time: str = ""  # 最后运行时间
    is_enabled: bool = True


class TaskScanner:
    """任务扫描器"""
    
    def __init__(self, tasks_dir: str = "tasks"):
        self.tasks_dir = Path(tasks_dir)
        self.discovered_tasks: List[TaskInfo] = []
    
    def scan_tasks(self) -> List[TaskInfo]:
        """扫描tasks文件夹中的所有Python任务文件"""
        self.discovered_tasks.clear()
        
        if not self.tasks_dir.exists():
            print(f"⚠️ 任务目录不存在: {self.tasks_dir}")
            return self.discovered_tasks
        
        # 扫描所有Python文件
        for py_file in self.tasks_dir.glob("*.py"):
            if py_file.name.startswith("__"):
                continue  # 跳过__init__.py等文件
            
            task_info = self._analyze_task_file(py_file)
            if task_info:
                self.discovered_tasks.append(task_info)
        
        print(f"📋 发现 {len(self.discovered_tasks)} 个任务文件")
        return self.discovered_tasks
    
    def _analyze_task_file(self, file_path: Path) -> Optional[TaskInfo]:
        """分析单个任务文件"""
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取任务信息
            task_info = self._extract_task_metadata(file_path, content)
            
            # 验证任务文件是否有效
            if self._validate_task_file(file_path):
                return task_info
            else:
                print(f"⚠️ 任务文件格式无效: {file_path.name}")
                return None
                
        except Exception as e:
            print(f"❌ 分析任务文件失败 {file_path.name}: {e}")
            return None
    
    def _extract_task_metadata(self, file_path: Path, content: str) -> TaskInfo:
        """从文件内容中提取任务元数据"""
        # 默认值
        name = file_path.stem
        description = "无描述"
        author = "未知"
        version = "1.0.0"
        category = "默认"
        
        # 尝试从文件头部注释提取信息
        lines = content.split('\n')
        
        for line in lines[:20]:  # 只检查前20行
            line = line.strip()
            
            if line.startswith('"""') or line.startswith("'''"):
                # 文档字符串，可能包含描述
                if "描述" in line or "description" in line.lower():
                    description = line.replace('"""', '').replace("'''", '').strip()
                    if description.startswith("描述:") or description.startswith("description:"):
                        description = description.split(":", 1)[1].strip()
            
            elif line.startswith('#'):
                # 注释行，查找特定标记
                comment = line[1:].strip()
                
                if comment.startswith("任务名称:") or comment.startswith("name:"):
                    name = comment.split(":", 1)[1].strip()
                elif comment.startswith("描述:") or comment.startswith("description:"):
                    description = comment.split(":", 1)[1].strip()
                elif comment.startswith("作者:") or comment.startswith("author:"):
                    author = comment.split(":", 1)[1].strip()
                elif comment.startswith("版本:") or comment.startswith("version:"):
                    version = comment.split(":", 1)[1].strip()
                elif comment.startswith("分类:") or comment.startswith("category:"):
                    category = comment.split(":", 1)[1].strip()
        
        return TaskInfo(
            name=name,
            file_path=str(file_path),
            description=description,
            author=author,
            version=version,
            category=category,
            last_modified=file_path.stat().st_mtime
        )
    
    def _validate_task_file(self, file_path: Path) -> bool:
        """验证任务文件是否符合规范"""
        try:
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(
                file_path.stem, file_path
            )
            if spec is None or spec.loader is None:
                return False
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 检查是否有必需的函数或类
            required_items = ['main', 'run', 'execute', 'Task']
            
            for item_name in required_items:
                if hasattr(module, item_name):
                    item = getattr(module, item_name)
                    if callable(item) or inspect.isclass(item):
                        return True
            
            # 如果没有找到标准入口，检查是否有可执行的函数
            for name, obj in inspect.getmembers(module):
                if (inspect.isfunction(obj) and 
                    not name.startswith('_') and 
                    name not in ['sys', 'os', 'import']):
                    return True
            
            return False
            
        except Exception as e:
            print(f"⚠️ 验证任务文件失败 {file_path.name}: {e}")
            return False
    
    def get_task_by_name(self, name: str) -> Optional[TaskInfo]:
        """根据名称获取任务信息"""
        for task in self.discovered_tasks:
            if task.name == name:
                return task
        return None
    
    def get_tasks_by_category(self, category: str) -> List[TaskInfo]:
        """根据分类获取任务列表"""
        return [task for task in self.discovered_tasks if task.category == category]
    
    def refresh_tasks(self) -> List[TaskInfo]:
        """刷新任务列表"""
        return self.scan_tasks()


def create_sample_task():
    """创建示例任务文件"""
    tasks_dir = Path("tasks")
    tasks_dir.mkdir(exist_ok=True)
    
    sample_task_content = '''#!/usr/bin/env python3
"""
示例任务 - 演示任务文件的基本结构
"""

# 任务名称: 示例数据处理任务
# 描述: 这是一个示例任务，用于演示任务系统的基本功能
# 作者: 系统管理员
# 版本: 1.0.0
# 分类: 示例

import time
import random
from datetime import datetime


def main():
    """任务主函数"""
    print(f"🚀 开始执行示例任务 - {datetime.now()}")
    
    # 模拟任务执行
    for i in range(5):
        print(f"📊 处理数据 {i+1}/5...")
        time.sleep(random.uniform(0.5, 1.5))
    
    print("✅ 示例任务执行完成")
    return {"status": "success", "processed_items": 5}


if __name__ == "__main__":
    result = main()
    print(f"任务结果: {result}")
'''
    
    sample_file = tasks_dir / "sample_task.py"
    if not sample_file.exists():
        with open(sample_file, 'w', encoding='utf-8') as f:
            f.write(sample_task_content)
        print(f"✅ 创建示例任务文件: {sample_file}")


if __name__ == "__main__":
    # 测试任务扫描器
    create_sample_task()
    
    scanner = TaskScanner()
    tasks = scanner.scan_tasks()
    
    print("\n📋 发现的任务:")
    for task in tasks:
        print(f"  📄 {task.name}")
        print(f"     描述: {task.description}")
        print(f"     作者: {task.author}")
        print(f"     分类: {task.category}")
        print(f"     文件: {task.file_path}")
        print()
