"""
主窗口UI界面
现代化、美观、简约的自动化任务调度管理界面
"""

import sys
from pathlib import Path
from typing import List, Optional
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QTableWidget, QTableWidgetItem, QPushButton,
    QLabel, QLineEdit, QComboBox, QTextEdit, QSplitter,
    QGroupBox, QGridLayout, QStatusBar,
    QHeaderView, QFrame, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.task_scanner import TaskScanner, TaskInfo


class ModernButton(QPushButton):
    """现代化按钮样式"""
    
    def __init__(self, text: str, button_type: str = "primary"):
        super().__init__(text)
        self.set_button_style(button_type)
    
    def set_button_style(self, button_type: str) -> None:
        """设置按钮样式"""
        base_style = """
            QPushButton {
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: 600;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: rgba(0, 0, 0, 0.1);
            }
            QPushButton:pressed {
                background-color: rgba(0, 0, 0, 0.2);
            }
        """
        
        if button_type == "primary":
            style = base_style + """
                QPushButton {
                    background-color: #3b82f6;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #2563eb;
                }
                QPushButton:pressed {
                    background-color: #1d4ed8;
                }
            """
        elif button_type == "success":
            style = base_style + """
                QPushButton {
                    background-color: #10b981;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #059669;
                }
                QPushButton:pressed {
                    background-color: #047857;
                }
            """
        elif button_type == "danger":
            style = base_style + """
                QPushButton {
                    background-color: #ef4444;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #dc2626;
                }
                QPushButton:pressed {
                    background-color: #b91c1c;
                }
            """
        elif button_type == "secondary":
            style = base_style + """
                QPushButton {
                    background-color: #6b7280;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #4b5563;
                }
                QPushButton:pressed {
                    background-color: #374151;
                }
            """
        
        self.setStyleSheet(style)


class TaskTableWidget(QTableWidget):
    """任务列表表格"""

    def __init__(self):
        super().__init__()
        self.tasks_data: List[TaskInfo] = []
        self.setup_table()

    def setup_table(self) -> None:
        """设置表格"""
        # 设置列
        headers = ["任务名称", "描述", "分类", "作者", "版本", "状态", "操作"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)
        
        # 设置表格样式
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e5e7eb;
                background-color: white;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #f3f4f6;
            }
            QTableWidget::item:selected {
                background-color: #eff6ff;
                color: #1e40af;
            }
            QHeaderView::section {
                background-color: #f9fafb;
                padding: 12px;
                border: none;
                border-bottom: 2px solid #e5e7eb;
                font-weight: 600;
                color: #374151;
            }
        """)
        
        # 设置表格属性
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.horizontalHeader().setStretchLastSection(True)
        self.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

    def load_tasks(self, tasks: List[TaskInfo]) -> None:
        """加载任务数据到表格"""
        self.tasks_data = tasks
        self.setRowCount(len(tasks))

        for row, task in enumerate(tasks):
            # 任务名称
            self.setItem(row, 0, QTableWidgetItem(task.name))

            # 描述
            self.setItem(row, 1, QTableWidgetItem(task.description))

            # 分类
            self.setItem(row, 2, QTableWidgetItem(task.category))

            # 作者
            self.setItem(row, 3, QTableWidgetItem(task.author))

            # 版本
            self.setItem(row, 4, QTableWidgetItem(task.version))

            # 状态
            status = "启用" if task.is_enabled else "禁用"
            self.setItem(row, 5, QTableWidgetItem(status))

            # 操作按钮
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(5, 2, 5, 2)

            run_btn = ModernButton("▶️ 运行", "success")
            run_btn.setMaximumHeight(30)
            edit_btn = ModernButton("📝 编辑", "primary")
            edit_btn.setMaximumHeight(30)

            action_layout.addWidget(run_btn)
            action_layout.addWidget(edit_btn)
            action_layout.addStretch()

            self.setCellWidget(row, 6, action_widget)

    def get_selected_task(self) -> Optional[TaskInfo]:
        """获取当前选中的任务"""
        current_row = self.currentRow()
        if 0 <= current_row < len(self.tasks_data):
            return self.tasks_data[current_row]
        return None


class LogWidget(QTextEdit):
    """日志显示组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_log_widget()
    
    def setup_log_widget(self) -> None:
        """设置日志组件"""
        self.setReadOnly(True)
        self.setStyleSheet("""
            QTextEdit {
                background-color: #1f2937;
                color: #f9fafb;
                border: 1px solid #374151;
                border-radius: 8px;
                padding: 12px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        
        # 添加示例日志
        self.append("[2024-07-02 12:00:00] 系统启动")
        self.append("[2024-07-02 12:00:01] 加载任务配置")
        self.append("[2024-07-02 12:00:02] 调度器初始化完成")


class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.task_scanner = TaskScanner()
        self.current_tasks: List[TaskInfo] = []
        self.setup_ui()
        self.setup_connections()
        self.load_tasks()
    
    def setup_ui(self) -> None:
        """设置UI界面"""
        self.setWindowTitle("自动化任务调度管理系统")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置应用样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8fafc;
            }
            QTabWidget::pane {
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                background-color: white;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #f3f4f6;
                padding: 12px 24px;
                margin-right: 4px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                color: #6b7280;
                font-weight: 500;
            }
            QTabBar::tab:selected {
                background-color: white;
                color: #1f2937;
                border-bottom: 2px solid #3b82f6;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建标题区域
        self.create_header(main_layout)
        
        # 创建选项卡
        self.create_tabs(main_layout)
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_header(self, parent_layout: QVBoxLayout) -> None:
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # 标题
        title_label = QLabel("🤖 自动化任务调度管理系统")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: 700;
                color: #1f2937;
                background: none;
                border: none;
            }
        """)
        
        # 快速操作按钮
        quick_actions = QHBoxLayout()

        self.btn_scan_tasks = ModernButton("🔍 扫描任务", "primary")
        self.btn_start_all = ModernButton("▶️ 启动所有", "success")
        self.btn_stop_all = ModernButton("⏹️ 停止所有", "danger")
        self.btn_refresh = ModernButton("🔄 刷新", "secondary")

        quick_actions.addWidget(self.btn_scan_tasks)
        quick_actions.addWidget(self.btn_start_all)
        quick_actions.addWidget(self.btn_stop_all)
        quick_actions.addWidget(self.btn_refresh)
        quick_actions.addStretch()
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addLayout(quick_actions)
        
        parent_layout.addWidget(header_frame)
    
    def create_tabs(self, parent_layout: QVBoxLayout) -> None:
        """创建选项卡"""
        self.tab_widget = QTabWidget()
        
        # 任务管理选项卡
        self.create_task_tab()
        
        # 日志监控选项卡
        self.create_log_tab()
        
        # 系统设置选项卡
        self.create_settings_tab()
        
        parent_layout.addWidget(self.tab_widget)
    
    def create_task_tab(self) -> None:
        """创建任务管理选项卡"""
        task_widget = QWidget()
        task_layout = QVBoxLayout(task_widget)
        task_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：任务列表
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 任务列表标题和搜索
        list_header = QHBoxLayout()
        list_title = QLabel("📋 任务列表")
        list_title.setStyleSheet("font-size: 18px; font-weight: 600; color: #1f2937;")
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 搜索任务...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #3b82f6;
            }
        """)
        
        list_header.addWidget(list_title)
        list_header.addStretch()
        list_header.addWidget(self.search_input)
        
        # 任务表格
        self.task_table = TaskTableWidget()
        
        left_layout.addLayout(list_header)
        left_layout.addWidget(self.task_table)
        
        # 右侧：任务详情
        right_widget = self.create_task_detail_panel()
        
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([800, 400])
        
        task_layout.addWidget(splitter)
        
        self.tab_widget.addTab(task_widget, "📋 任务管理")
    
    def create_task_detail_panel(self) -> QWidget:
        """创建任务详情面板"""
        detail_widget = QWidget()
        detail_layout = QVBoxLayout(detail_widget)
        
        # 详情标题
        detail_title = QLabel("📝 任务详情")
        detail_title.setStyleSheet("font-size: 18px; font-weight: 600; color: #1f2937;")
        detail_layout.addWidget(detail_title)
        
        # 任务信息表单
        form_group = QGroupBox("基本信息")
        form_layout = QGridLayout(form_group)
        
        # 任务名称
        form_layout.addWidget(QLabel("任务名称:"), 0, 0)
        self.task_name_input = QLineEdit()
        form_layout.addWidget(self.task_name_input, 0, 1)
        
        # 执行频率
        form_layout.addWidget(QLabel("执行频率:"), 1, 0)
        self.frequency_combo = QComboBox()
        self.frequency_combo.addItems(["手动执行", "每分钟", "每小时", "每天", "每周", "每月"])
        form_layout.addWidget(self.frequency_combo, 1, 1)
        
        # 任务状态
        form_layout.addWidget(QLabel("任务状态:"), 2, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["停止", "运行中", "暂停", "错误"])
        form_layout.addWidget(self.status_combo, 2, 1)
        
        detail_layout.addWidget(form_group)
        
        # 任务描述
        desc_group = QGroupBox("任务描述")
        desc_layout = QVBoxLayout(desc_group)
        self.task_description = QTextEdit()
        self.task_description.setMaximumHeight(100)
        desc_layout.addWidget(self.task_description)
        detail_layout.addWidget(desc_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        self.btn_save_task = ModernButton("💾 保存配置", "primary")
        self.btn_run_task = ModernButton("▶️ 运行任务", "success")
        self.btn_open_file = ModernButton("📝 编辑文件", "secondary")

        button_layout.addWidget(self.btn_save_task)
        button_layout.addWidget(self.btn_run_task)
        button_layout.addWidget(self.btn_open_file)
        button_layout.addStretch()
        
        detail_layout.addLayout(button_layout)
        detail_layout.addStretch()
        
        return detail_widget
    
    def create_log_tab(self) -> None:
        """创建日志监控选项卡"""
        log_widget = QWidget()
        log_layout = QVBoxLayout(log_widget)
        log_layout.setContentsMargins(20, 20, 20, 20)
        
        # 日志标题和控制
        log_header = QHBoxLayout()
        log_title = QLabel("📊 系统日志")
        log_title.setStyleSheet("font-size: 18px; font-weight: 600; color: #1f2937;")
        
        # 日志级别过滤
        level_combo = QComboBox()
        level_combo.addItems(["全部", "INFO", "WARNING", "ERROR", "DEBUG"])
        
        clear_btn = ModernButton("🗑️ 清空日志", "secondary")
        export_btn = ModernButton("📤 导出日志", "primary")
        
        log_header.addWidget(log_title)
        log_header.addStretch()
        log_header.addWidget(QLabel("日志级别:"))
        log_header.addWidget(level_combo)
        log_header.addWidget(clear_btn)
        log_header.addWidget(export_btn)
        
        # 日志显示区域
        self.log_display = LogWidget()
        
        log_layout.addLayout(log_header)
        log_layout.addWidget(self.log_display)
        
        self.tab_widget.addTab(log_widget, "📊 日志监控")
    
    def create_settings_tab(self) -> None:
        """创建系统设置选项卡"""
        settings_widget = QWidget()
        settings_layout = QVBoxLayout(settings_widget)
        settings_layout.setContentsMargins(20, 20, 20, 20)
        
        # 设置标题
        settings_title = QLabel("⚙️ 系统设置")
        settings_title.setStyleSheet("font-size: 18px; font-weight: 600; color: #1f2937;")
        settings_layout.addWidget(settings_title)
        
        # 设置表单
        settings_form = QGroupBox("基本设置")
        form_layout = QGridLayout(settings_form)
        
        # 最大并发任务数
        form_layout.addWidget(QLabel("最大并发任务数:"), 0, 0)
        max_tasks_input = QLineEdit("5")
        form_layout.addWidget(max_tasks_input, 0, 1)
        
        # 日志保留天数
        form_layout.addWidget(QLabel("日志保留天数:"), 1, 0)
        log_days_input = QLineEdit("30")
        form_layout.addWidget(log_days_input, 1, 1)
        
        # 自动启动
        form_layout.addWidget(QLabel("开机自动启动:"), 2, 0)
        auto_start_combo = QComboBox()
        auto_start_combo.addItems(["否", "是"])
        form_layout.addWidget(auto_start_combo, 2, 1)
        
        settings_layout.addWidget(settings_form)
        
        # 保存按钮
        save_settings_btn = ModernButton("💾 保存设置", "primary")
        settings_layout.addWidget(save_settings_btn)
        settings_layout.addStretch()
        
        self.tab_widget.addTab(settings_widget, "⚙️ 系统设置")
    
    def create_status_bar(self) -> None:
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 状态信息
        self.status_bar.showMessage("系统就绪")
        
        # 添加永久部件
        self.task_count_label = QLabel("任务总数: 0")
        self.running_count_label = QLabel("运行中: 0")
        
        self.status_bar.addPermanentWidget(self.task_count_label)
        self.status_bar.addPermanentWidget(self.running_count_label)
    
    def setup_connections(self) -> None:
        """设置信号连接"""
        # 快速操作按钮
        self.btn_scan_tasks.clicked.connect(self.scan_tasks)
        self.btn_refresh.clicked.connect(self.refresh_tasks)

        # 任务表格选择事件
        self.task_table.itemSelectionChanged.connect(self.on_task_selected)

        # 搜索框
        self.search_input.textChanged.connect(self.filter_tasks)

    def load_tasks(self) -> None:
        """加载任务列表"""
        try:
            self.current_tasks = self.task_scanner.scan_tasks()
            self.task_table.load_tasks(self.current_tasks)
            self.update_status_bar()

            if self.current_tasks:
                self.log_display.append(f"[{self.get_current_time()}] ✅ 成功加载 {len(self.current_tasks)} 个任务")
            else:
                self.log_display.append(f"[{self.get_current_time()}] ⚠️ 未发现任何任务文件")

        except Exception as e:
            self.log_display.append(f"[{self.get_current_time()}] ❌ 加载任务失败: {e}")

    def scan_tasks(self) -> None:
        """扫描任务"""
        self.log_display.append(f"[{self.get_current_time()}] 🔍 开始扫描任务文件...")
        self.load_tasks()

    def refresh_tasks(self) -> None:
        """刷新任务列表"""
        self.log_display.append(f"[{self.get_current_time()}] 🔄 刷新任务列表...")
        self.load_tasks()

    def filter_tasks(self, text: str) -> None:
        """过滤任务列表"""
        if not text:
            self.task_table.load_tasks(self.current_tasks)
            return

        filtered_tasks = [
            task for task in self.current_tasks
            if text.lower() in task.name.lower() or
               text.lower() in task.description.lower() or
               text.lower() in task.category.lower()
        ]
        self.task_table.load_tasks(filtered_tasks)

    def on_task_selected(self) -> None:
        """任务选择事件"""
        selected_task = self.task_table.get_selected_task()
        if selected_task:
            self.load_task_details(selected_task)

    def load_task_details(self, task: TaskInfo) -> None:
        """加载任务详情"""
        self.task_name_input.setText(task.name)
        self.task_description.setText(task.description)

        # 设置状态
        status_text = "启用" if task.is_enabled else "禁用"
        index = self.status_combo.findText(status_text)
        if index >= 0:
            self.status_combo.setCurrentIndex(index)

    def update_status_bar(self) -> None:
        """更新状态栏"""
        total_tasks = len(self.current_tasks)
        enabled_tasks = sum(1 for task in self.current_tasks if task.is_enabled)

        self.task_count_label.setText(f"任务总数: {total_tasks}")
        self.running_count_label.setText(f"启用任务: {enabled_tasks}")

        if total_tasks > 0:
            self.status_bar.showMessage(f"就绪 - 发现 {total_tasks} 个任务")
        else:
            self.status_bar.showMessage("就绪 - 未发现任务文件")

    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("自动化任务调度管理系统")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
