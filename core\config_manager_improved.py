"""
配置管理器 - 处理任务配置的持久化存储（Pythonic 改进版）
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum


class TaskStatus(Enum):
    """任务状态枚举"""
    WAITING = "等待中"
    RUNNING = "执行中"
    DISABLED = "已禁用"


class ConfigManagerError(Exception):
    """配置管理器基础异常"""
    pass


class ConfigLoadError(ConfigManagerError):
    """配置加载异常"""
    pass


class ConfigSaveError(ConfigManagerError):
    """配置保存异常"""
    pass


@dataclass
class TaskConfig:
    """任务配置数据类
    
    Attributes:
        name: 任务名称
        status: 任务状态
        frequency_minutes: 执行频率（分钟）
        next_run_time: 下次运行时间
        run_count: 已运行次数
        last_run_time: 最后运行时间
        is_enabled: 是否启用
    """
    name: str
    status: str = TaskStatus.WAITING.value
    frequency_minutes: int = 60
    next_run_time: str = ""
    run_count: int = 0
    last_run_time: str = ""
    is_enabled: bool = True
    
    def __post_init__(self) -> None:
        """初始化后处理，设置默认的下次运行时间"""
        if not self.next_run_time and self.is_enabled:
            self._calculate_next_run_time()
    
    def _calculate_next_run_time(self) -> None:
        """计算下次运行时间"""
        next_time = datetime.now() + timedelta(minutes=self.frequency_minutes)
        self.next_run_time = next_time.strftime("%Y-%m-%d %H:%M:%S")


class ConfigManager:
    """配置管理器
    
    负责任务配置的加载、保存、更新和管理。
    提供线程安全的配置操作和完善的错误处理。
    """
    
    # 类常量
    DEFAULT_CONFIG_FILE = "config/task_configs.json"
    DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
    
    def __init__(self, config_file: str = DEFAULT_CONFIG_FILE):
        """初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = Path(config_file)
        self.task_configs: Dict[str, TaskConfig] = {}
        self.logger = self._setup_logger()
        
        self._ensure_config_directory_exists()
        self.load_configs()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _ensure_config_directory_exists(self) -> None:
        """确保配置目录存在"""
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
    
    def _log_safely(self, message: str, level: str = "info") -> None:
        """安全的日志记录方法
        
        Args:
            message: 日志消息
            level: 日志级别
        """
        try:
            log_method = getattr(self.logger, level.lower(), self.logger.info)
            log_method(message)
        except (AttributeError, OSError) as e:
            # 日志记录失败时使用备用输出
            try:
                print(f"[LOG_FALLBACK] {message}")
            except OSError:
                # 连print都失败时静默忽略
                pass
    
    def load_configs(self) -> None:
        """加载配置文件
        
        Raises:
            ConfigLoadError: 配置加载失败时抛出
        """
        if not self.config_file.exists():
            self.task_configs = {}
            self._log_safely("配置文件不存在，创建空配置")
            return
        
        try:
            config_data = self._read_config_file()
            self.task_configs = self._parse_config_data(config_data)
            self._log_safely(f"✅ 成功加载任务配置: {len(self.task_configs)} 个")
            
        except (json.JSONDecodeError, KeyError, TypeError) as e:
            error_msg = f"配置文件格式错误: {e}"
            self._log_safely(error_msg, "error")
            raise ConfigLoadError(error_msg) from e
        except OSError as e:
            error_msg = f"配置文件读取失败: {e}"
            self._log_safely(error_msg, "error")
            raise ConfigLoadError(error_msg) from e
    
    def _read_config_file(self) -> Dict[str, Any]:
        """读取配置文件内容"""
        with open(self.config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _parse_config_data(self, data: Dict[str, Any]) -> Dict[str, TaskConfig]:
        """解析配置数据为TaskConfig对象"""
        configs = {}
        for name, config_data in data.items():
            if not isinstance(config_data, dict):
                continue
            try:
                configs[name] = TaskConfig(**config_data)
            except TypeError as e:
                self._log_safely(f"跳过无效配置 {name}: {e}", "warning")
        return configs
    
    def save_configs(self) -> None:
        """保存配置文件
        
        Raises:
            ConfigSaveError: 配置保存失败时抛出
        """
        try:
            config_data = self._serialize_configs()
            self._write_config_file(config_data)
            self._log_safely(f"💾 成功保存任务配置: {len(self.task_configs)} 个")
            
        except OSError as e:
            error_msg = f"配置文件写入失败: {e}"
            self._log_safely(error_msg, "error")
            raise ConfigSaveError(error_msg) from e
        except (TypeError, ValueError) as e:
            error_msg = f"配置数据序列化失败: {e}"
            self._log_safely(error_msg, "error")
            raise ConfigSaveError(error_msg) from e
    
    def _serialize_configs(self) -> Dict[str, Dict[str, Any]]:
        """序列化配置为可保存的格式"""
        return {
            name: asdict(config) 
            for name, config in self.task_configs.items()
        }
    
    def _write_config_file(self, data: Dict[str, Dict[str, Any]]) -> None:
        """写入配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def cleanup_missing_tasks(self, existing_task_names: List[str]) -> None:
        """清理不存在的任务配置
        
        Args:
            existing_task_names: 现有任务名称列表
        """
        missing_tasks = self._find_missing_tasks(existing_task_names)
        
        if not missing_tasks:
            self._log_safely("✅ 所有任务配置都有对应的任务文件")
            return
        
        self._remove_missing_tasks(missing_tasks)
        self.save_configs()
    
    def _find_missing_tasks(self, existing_names: List[str]) -> List[str]:
        """查找缺失的任务"""
        return [
            task_name for task_name in self.task_configs.keys()
            if task_name not in existing_names
        ]
    
    def _remove_missing_tasks(self, missing_tasks: List[str]) -> None:
        """移除缺失的任务配置"""
        self._log_safely(f"🧹 清理不存在的任务配置: {len(missing_tasks)} 个")
        for task_name in missing_tasks:
            self._log_safely(f"  🗑️ 删除配置: {task_name}")
            del self.task_configs[task_name]
    
    def get_task_config(self, task_name: str) -> TaskConfig:
        """获取任务配置，不存在时创建默认配置
        
        Args:
            task_name: 任务名称
            
        Returns:
            任务配置对象
        """
        if task_name not in self.task_configs:
            self.task_configs[task_name] = TaskConfig(name=task_name)
            self.save_configs()
        
        return self.task_configs[task_name]
    
    def update_task_config(self, task_name: str, **kwargs) -> None:
        """更新任务配置
        
        Args:
            task_name: 任务名称
            **kwargs: 要更新的配置项
        """
        config = self.get_task_config(task_name)
        
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
            else:
                self._log_safely(f"忽略未知配置项: {key}", "warning")
        
        self.save_configs()
    
    def set_task_status(self, task_name: str, status: TaskStatus) -> None:
        """设置任务状态
        
        Args:
            task_name: 任务名称
            status: 任务状态枚举
        """
        self.update_task_config(task_name, status=status.value)
    
    def increment_run_count(self, task_name: str) -> None:
        """增加运行次数并更新相关时间
        
        Args:
            task_name: 任务名称
        """
        config = self.get_task_config(task_name)
        config.run_count += 1
        config.last_run_time = datetime.now().strftime(self.DATETIME_FORMAT)
        
        if config.is_enabled and config.frequency_minutes > 0:
            config._calculate_next_run_time()
        
        self.save_configs()
    
    def enable_task(self, task_name: str, enabled: bool = True) -> None:
        """启用/禁用任务
        
        Args:
            task_name: 任务名称
            enabled: 是否启用
        """
        status = TaskStatus.WAITING if enabled else TaskStatus.DISABLED
        self.update_task_config(task_name, is_enabled=enabled, status=status.value)
        
        if enabled:
            config = self.get_task_config(task_name)
            config._calculate_next_run_time()
            self.save_configs()
    
    def set_frequency(self, task_name: str, minutes: int) -> None:
        """设置执行频率
        
        Args:
            task_name: 任务名称
            minutes: 频率（分钟）
            
        Raises:
            ValueError: 频率值无效时抛出
        """
        if minutes <= 0:
            raise ValueError(f"执行频率必须大于0，得到: {minutes}")
        
        self.update_task_config(task_name, frequency_minutes=minutes)
        
        config = self.get_task_config(task_name)
        if config.is_enabled:
            config._calculate_next_run_time()
            self.save_configs()
    
    def get_all_configs(self) -> Dict[str, TaskConfig]:
        """获取所有配置的副本
        
        Returns:
            所有任务配置的字典副本
        """
        return self.task_configs.copy()
    
    def remove_task_config(self, task_name: str) -> bool:
        """移除任务配置
        
        Args:
            task_name: 任务名称
            
        Returns:
            是否成功移除
        """
        if task_name in self.task_configs:
            del self.task_configs[task_name]
            self.save_configs()
            return True
        return False
    
    def get_tasks_by_status(self, status: TaskStatus) -> List[str]:
        """根据状态获取任务列表
        
        Args:
            status: 任务状态枚举
            
        Returns:
            符合状态的任务名称列表
        """
        return [
            name for name, config in self.task_configs.items()
            if config.status == status.value
        ]
    
    def get_enabled_tasks(self) -> List[str]:
        """获取启用的任务列表
        
        Returns:
            启用任务的名称列表
        """
        return [
            name for name, config in self.task_configs.items()
            if config.is_enabled
        ]
    
    def get_stats(self) -> Dict[str, int]:
        """获取配置统计信息
        
        Returns:
            包含各种统计数据的字典
        """
        total_tasks = len(self.task_configs)
        enabled_tasks = len(self.get_enabled_tasks())
        waiting_tasks = len(self.get_tasks_by_status(TaskStatus.WAITING))
        running_tasks = len(self.get_tasks_by_status(TaskStatus.RUNNING))
        disabled_tasks = len(self.get_tasks_by_status(TaskStatus.DISABLED))
        
        return {
            "total_tasks": total_tasks,
            "enabled_tasks": enabled_tasks,
            "waiting_tasks": waiting_tasks,
            "running_tasks": running_tasks,
            "disabled_tasks": disabled_tasks,
        }


if __name__ == "__main__":
    # 示例用法和测试
    try:
        manager = ConfigManager()
        
        # 创建示例配置
        manager.update_task_config("示例任务", frequency_minutes=30)
        manager.update_task_config("数据采集", frequency_minutes=120)
        manager.set_task_status("数据采集", TaskStatus.RUNNING)
        
        # 显示所有配置
        print("\n📋 所有任务配置:")
        for name, config in manager.get_all_configs().items():
            print(f"  {name}: {config.status}, 频率: {config.frequency_minutes}分钟")
        
        # 显示统计信息
        stats = manager.get_stats()
        print(f"\n📊 统计信息: {stats}")
        
    except ConfigManagerError as e:
        print(f"配置管理器错误: {e}")
    except Exception as e:
        print(f"未知错误: {e}") 