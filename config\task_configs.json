{"长时间任务": {"name": "长时间任务", "status": "执行中", "frequency_minutes": 1, "next_run_time": "2025-07-02 23:04:46", "run_count": 11, "last_run_time": "2025-07-02 23:03:46", "is_enabled": true}, "中等延迟任务": {"name": "中等延迟任务", "status": "执行中", "frequency_minutes": 1, "next_run_time": "2025-07-02 23:04:44", "run_count": 11, "last_run_time": "2025-07-02 23:03:44", "is_enabled": true}, "快速测试任务": {"name": "快速测试任务", "status": "已禁用", "frequency_minutes": 1, "next_run_time": "2025-07-02 22:14:13", "run_count": 6, "last_run_time": "2025-07-02 22:13:13", "is_enabled": false}, "状态监控任务": {"name": "状态监控任务", "status": "执行中", "frequency_minutes": 1, "next_run_time": "2025-07-02 23:09:08", "run_count": 15, "last_run_time": "2025-07-02 23:08:08", "is_enabled": true}, "修复UI状态同步": {"name": "修复UI状态同步", "status": "等待中", "frequency_minutes": 1, "next_run_time": "2025-07-02 23:10:30", "run_count": 9, "last_run_time": "2025-07-02 23:09:30", "is_enabled": true}, "功能完整性验证": {"name": "功能完整性验证", "status": "等待中", "frequency_minutes": 2, "next_run_time": "2025-07-02 23:10:10", "run_count": 3, "last_run_time": "2025-07-02 23:08:10", "is_enabled": true}, "修复过期时间处理": {"name": "修复过期时间处理", "status": "执行中", "frequency_minutes": 1, "next_run_time": "2025-07-02 23:10:30", "run_count": 9, "last_run_time": "2025-07-02 23:09:30", "is_enabled": true}, "UI实时刷新机制": {"name": "UI实时刷新机制", "status": "执行中", "frequency_minutes": 1, "next_run_time": "2025-07-02 23:09:09", "run_count": 5, "last_run_time": "2025-07-02 23:08:09", "is_enabled": true}, "调度器性能优化": {"name": "调度器性能优化", "status": "执行中", "frequency_minutes": 1, "next_run_time": "2025-07-02 23:09:08", "run_count": 5, "last_run_time": "2025-07-02 23:08:08", "is_enabled": true}, "系统稳定性检查": {"name": "系统稳定性检查", "status": "执行中", "frequency_minutes": 1, "next_run_time": "2025-07-02 23:09:12", "run_count": 5, "last_run_time": "2025-07-02 23:08:12", "is_enabled": true}, "持久延迟任务": {"name": "持久延迟任务", "status": "执行中", "frequency_minutes": 1, "next_run_time": "2025-07-02 23:04:53", "run_count": 1, "last_run_time": "2025-07-02 23:03:53", "is_enabled": true}}