#!/usr/bin/env python3
"""
自动化任务调度管理系统 - 主启动文件
支持GUI模式和命令行模式
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def run_gui_mode():
    """启动GUI模式"""
    try:
        from ui.main_window import main as gui_main
        print("🚀 启动GUI模式...")
        gui_main()
    except ImportError as e:
        print(f"❌ GUI模式启动失败: {e}")
        print("请确保已安装PyQt6: pip install PyQt6")
        sys.exit(1)


def run_cli_mode():
    """启动命令行模式"""
    print("🖥️ 命令行模式暂未实现")
    print("请使用GUI模式: python main.py --gui")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="自动化任务调度管理系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py              # 默认启动GUI模式
  python main.py --gui         # 启动GUI模式
  python main.py --cli         # 启动命令行模式
        """
    )
    
    parser.add_argument(
        "--gui", 
        action="store_true", 
        default=True,
        help="启动GUI模式 (默认)"
    )
    
    parser.add_argument(
        "--cli", 
        action="store_true",
        help="启动命令行模式"
    )
    
    parser.add_argument(
        "--version", 
        action="version", 
        version="自动化任务调度管理系统 v1.0.0"
    )
    
    args = parser.parse_args()
    
    # 如果没有指定模式，默认GUI模式
    if not args.cli:
        run_gui_mode()
    else:
        run_cli_mode()


if __name__ == "__main__":
    main()
