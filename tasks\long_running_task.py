#!/usr/bin/env python3
"""
长时间任务 - 3分钟间隔执行，模拟复杂的长时间处理任务
"""

# 任务名称: 长时间任务
# 描述: 每3分钟执行一次，模拟需要较长时间的复杂处理任务
# 作者: 测试
# 版本: 1.0.0
# 分类: 测试

import sys
import os
import time
import random
from datetime import datetime

# 导入全局配置
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared.global_config import G


def run():
    """任务执行函数"""
    start_time = datetime.now()
    print(f"⏳ 长时间任务开始执行 - {start_time.strftime('%H:%M:%S')}")
    
    # 记录任务统计
    current_count = G.get("test_stats.long_task.run_count", 0)
    G.test_stats.long_task.run_count = current_count + 1
    G.test_stats.long_task.last_run = start_time.strftime("%Y-%m-%d %H:%M:%S")
    
    print(f"📊 这是第 {G.test_stats.long_task.run_count} 次执行")
    
    # 模拟复杂的多阶段处理
    phases = [
        ("🔍 数据分析阶段", 2.0, 3.0),
        ("🔄 数据转换阶段", 1.5, 2.5),
        ("💾 数据存储阶段", 1.0, 2.0),
        ("📊 报告生成阶段", 1.5, 2.5),
        ("✅ 验证检查阶段", 1.0, 1.5)
    ]
    
    phase_results = []
    total_items_processed = 0
    
    for i, (phase_name, min_time, max_time) in enumerate(phases):
        print(f"\n{phase_name} ({i+1}/{len(phases)})")
        
        # 模拟该阶段的处理时间
        phase_duration = random.uniform(min_time, max_time)
        items_in_phase = random.randint(2, 5)
        
        print(f"  📦 处理 {items_in_phase} 个项目...")
        
        for j in range(items_in_phase):
            item_time = phase_duration / items_in_phase
            time.sleep(item_time)
            print(f"    ✓ 项目 {j+1}/{items_in_phase} 完成")
        
        total_items_processed += items_in_phase
        
        phase_results.append({
            "phase": phase_name,
            "duration": round(phase_duration, 2),
            "items_processed": items_in_phase
        })
        
        print(f"  ✅ {phase_name} 完成 - 耗时: {phase_duration:.1f}秒")
    
    # 记录完成时间和统计
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    G.test_stats.long_task.last_duration = duration
    G.test_stats.long_task.last_processed_count = total_items_processed
    G.test_stats.long_task.total_processed = G.get("test_stats.long_task.total_processed", 0) + total_items_processed
    G.test_stats.long_task.avg_duration = G.get("test_stats.long_task.avg_duration", 0)
    
    # 计算平均执行时间
    if G.test_stats.long_task.run_count == 1:
        G.test_stats.long_task.avg_duration = duration
    else:
        old_avg = G.test_stats.long_task.avg_duration
        G.test_stats.long_task.avg_duration = (old_avg * (G.test_stats.long_task.run_count - 1) + duration) / G.test_stats.long_task.run_count
    
    G.test_stats.long_task.status = "success"
    
    print(f"\n📈 任务统计:")
    print(f"  总执行次数: {G.test_stats.long_task.run_count}")
    print(f"  累计处理项目: {G.test_stats.long_task.total_processed}")
    print(f"  平均执行时间: {G.test_stats.long_task.avg_duration:.1f}秒")
    print(f"✅ 长时间任务完成 - 本次耗时: {duration:.1f}秒")
    
    return {
        "status": "success",
        "duration": duration,
        "run_count": G.test_stats.long_task.run_count,
        "processed_items": total_items_processed,
        "total_processed": G.test_stats.long_task.total_processed,
        "avg_duration": round(G.test_stats.long_task.avg_duration, 1),
        "phases": phase_results,
        "message": f"成功完成 {len(phases)} 个处理阶段，处理 {total_items_processed} 个项目"
    }


if __name__ == "__main__":
    result = run()
    print(f"📋 任务结果: {result}")
