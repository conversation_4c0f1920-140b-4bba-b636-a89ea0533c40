"""
配置管理器 - 处理任务配置的持久化存储
"""

import json
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta


@dataclass
class TaskConfig:
    """任务配置数据类"""
    name: str
    status: str = "等待中"  # 执行中、等待中、已禁用
    frequency_minutes: int = 60  # 执行频率（分钟）
    next_run_time: str = ""  # 下次运行时间
    run_count: int = 0  # 已运行次数
    last_run_time: str = ""  # 最后运行时间
    is_enabled: bool = True
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.next_run_time and self.is_enabled:
            # 如果没有设置下次运行时间，设置为当前时间+频率
            next_time = datetime.now() + timedelta(minutes=self.frequency_minutes)
            self.next_run_time = next_time.strftime("%Y-%m-%d %H:%M:%S")


class ConfigManager:
    """配置管理器"""

    def __init__(self, config_file: str = "config/task_configs.json"):
        self.config_file = Path(config_file)
        # 确保配置目录存在
        self.config_file.parent.mkdir(exist_ok=True)
        self.task_configs: Dict[str, TaskConfig] = {}
        self.load_configs()
    
    def load_configs(self) -> None:
        """加载配置文件"""
        if not self.config_file.exists():
            self.task_configs = {}
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.task_configs = {}
            for name, config_data in data.items():
                self.task_configs[name] = TaskConfig(**config_data)
                
            print(f"✅ 加载任务配置: {len(self.task_configs)} 个")
            
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            self.task_configs = {}
    
    def save_configs(self) -> None:
        """保存配置文件"""
        try:
            data = {}
            for name, config in self.task_configs.items():
                data[name] = asdict(config)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 保存任务配置: {len(self.task_configs)} 个")
            
        except Exception as e:
            print(f"❌ 保存配置文件失败: {e}")

    def cleanup_missing_tasks(self, existing_task_names: List[str]) -> None:
        """清理不存在的任务配置"""
        tasks_to_remove = []

        for task_name in self.task_configs.keys():
            if task_name not in existing_task_names:
                tasks_to_remove.append(task_name)

        if tasks_to_remove:
            print(f"🧹 清理不存在的任务配置: {len(tasks_to_remove)} 个")
            for task_name in tasks_to_remove:
                print(f"  🗑️ 删除配置: {task_name}")
                del self.task_configs[task_name]

            # 保存清理后的配置
            self.save_configs()
        else:
            print("✅ 所有任务配置都有对应的任务文件")

    def get_task_config(self, task_name: str) -> TaskConfig:
        """获取任务配置"""
        if task_name not in self.task_configs:
            # 创建默认配置
            self.task_configs[task_name] = TaskConfig(name=task_name)
            self.save_configs()
        
        return self.task_configs[task_name]
    
    def update_task_config(self, task_name: str, **kwargs) -> None:
        """更新任务配置"""
        config = self.get_task_config(task_name)
        
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        self.save_configs()
    
    def set_task_status(self, task_name: str, status: str) -> None:
        """设置任务状态"""
        self.update_task_config(task_name, status=status)
    
    def increment_run_count(self, task_name: str) -> None:
        """增加运行次数"""
        config = self.get_task_config(task_name)
        config.run_count += 1
        config.last_run_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 计算下次运行时间
        if config.is_enabled and config.frequency_minutes > 0:
            next_time = datetime.now() + timedelta(minutes=config.frequency_minutes)
            config.next_run_time = next_time.strftime("%Y-%m-%d %H:%M:%S")
        
        self.save_configs()
    
    def enable_task(self, task_name: str, enabled: bool = True) -> None:
        """启用/禁用任务"""
        status = "等待中" if enabled else "已禁用"
        self.update_task_config(task_name, is_enabled=enabled, status=status)
        
        if enabled:
            # 重新计算下次运行时间
            config = self.get_task_config(task_name)
            next_time = datetime.now() + timedelta(minutes=config.frequency_minutes)
            config.next_run_time = next_time.strftime("%Y-%m-%d %H:%M:%S")
            self.save_configs()
    
    def set_frequency(self, task_name: str, minutes: int) -> None:
        """设置执行频率"""
        self.update_task_config(task_name, frequency_minutes=minutes)
        
        # 重新计算下次运行时间
        config = self.get_task_config(task_name)
        if config.is_enabled:
            next_time = datetime.now() + timedelta(minutes=minutes)
            config.next_run_time = next_time.strftime("%Y-%m-%d %H:%M:%S")
            self.save_configs()
    
    def get_all_configs(self) -> Dict[str, TaskConfig]:
        """获取所有配置"""
        return self.task_configs.copy()
    
    def remove_task_config(self, task_name: str) -> None:
        """移除任务配置"""
        if task_name in self.task_configs:
            del self.task_configs[task_name]
            self.save_configs()
    
    def get_tasks_by_status(self, status: str) -> List[str]:
        """根据状态获取任务列表"""
        return [
            name for name, config in self.task_configs.items()
            if config.status == status
        ]
    
    def get_enabled_tasks(self) -> List[str]:
        """获取启用的任务列表"""
        return [
            name for name, config in self.task_configs.items()
            if config.is_enabled
        ]


if __name__ == "__main__":
    # 测试配置管理器
    manager = ConfigManager()
    
    # 创建示例配置
    manager.update_task_config("示例任务", frequency_minutes=30)
    manager.update_task_config("数据采集", frequency_minutes=120, status="执行中")
    
    print("\n📋 所有任务配置:")
    for name, config in manager.get_all_configs().items():
        print(f"  {name}: {config.status}, 频率: {config.frequency_minutes}分钟")
