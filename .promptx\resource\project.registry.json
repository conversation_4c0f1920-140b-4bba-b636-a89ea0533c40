{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-02T15:54:11.292Z", "updatedAt": "2025-07-02T15:54:11.293Z", "resourceCount": 6}, "resources": [{"id": "pythonic-dev-assistant", "source": "project", "protocol": "role", "name": "Pythonic Dev Assistant 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/pythonic-dev-assistant/pythonic-dev-assistant.role.md", "metadata": {"createdAt": "2025-07-02T15:54:11.293Z", "updatedAt": "2025-07-02T15:54:11.293Z", "scannedAt": "2025-07-02T15:54:11.293Z"}}, {"id": "pythonic-thinking", "source": "project", "protocol": "thought", "name": "Pythonic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/pythonic-dev-assistant/thought/pythonic-thinking.thought.md", "metadata": {"createdAt": "2025-07-02T15:54:11.293Z", "updatedAt": "2025-07-02T15:54:11.293Z", "scannedAt": "2025-07-02T15:54:11.293Z"}}, {"id": "code-review-process", "source": "project", "protocol": "execution", "name": "Code Review Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/pythonic-dev-assistant/execution/code-review-process.execution.md", "metadata": {"createdAt": "2025-07-02T15:54:11.293Z", "updatedAt": "2025-07-02T15:54:11.293Z", "scannedAt": "2025-07-02T15:54:11.293Z"}}, {"id": "pythonic-workflow", "source": "project", "protocol": "execution", "name": "Pythonic Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/pythonic-dev-assistant/execution/pythonic-workflow.execution.md", "metadata": {"createdAt": "2025-07-02T15:54:11.293Z", "updatedAt": "2025-07-02T15:54:11.293Z", "scannedAt": "2025-07-02T15:54:11.293Z"}}, {"id": "python-standards", "source": "project", "protocol": "knowledge", "name": "Python Standards 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/pythonic-dev-assistant/knowledge/python-standards.knowledge.md", "metadata": {"createdAt": "2025-07-02T15:54:11.293Z", "updatedAt": "2025-07-02T15:54:11.293Z", "scannedAt": "2025-07-02T15:54:11.293Z"}}, {"id": "pythonic-patterns", "source": "project", "protocol": "knowledge", "name": "Pythonic Patterns 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/pythonic-dev-assistant/knowledge/pythonic-patterns.knowledge.md", "metadata": {"createdAt": "2025-07-02T15:54:11.293Z", "updatedAt": "2025-07-02T15:54:11.293Z", "scannedAt": "2025-07-02T15:54:11.293Z"}}], "stats": {"totalResources": 6, "byProtocol": {"role": 1, "thought": 1, "execution": 2, "knowledge": 2}, "bySource": {"project": 6}}}