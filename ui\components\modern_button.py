"""
现代化按钮组件
提供统一的按钮样式和行为
"""

from typing import Optional, Callable
from PyQt6.QtWidgets import QPushButton
from PyQt6.QtCore import pyqtSignal, QTimer
from PyQt6.QtGui import QIcon

from ..styles.button_styles import ButtonStyles


class ModernButton(QPushButton):
    """现代化按钮组件
    
    特性：
    - 统一的样式系统
    - 支持多种预设样式
    - 支持图标
    - 支持加载状态
    - 支持禁用状态
    """
    
    # 自定义信号
    clicked_with_data = pyqtSignal(object)  # 携带数据的点击信号
    
    def __init__(
        self, 
        text: str, 
        button_type: str = "primary",
        icon: Optional[QIcon] = None,
        data: Optional[object] = None
    ):
        """初始化现代化按钮
        
        Args:
            text: 按钮文本
            button_type: 按钮类型（primary, success, danger, secondary, outline, text）
            icon: 按钮图标
            data: 关联数据，点击时会通过 clicked_with_data 信号发送
        """
        super().__init__(text)
        self.button_type = button_type
        self.associated_data = data
        self.is_loading = False
        self.original_text = text
        
        # 设置图标
        if icon:
            self.setIcon(icon)
        
        # 设置样式
        self._apply_style()
        
        # 连接信号
        self.clicked.connect(self._on_clicked)
    
    def _apply_style(self) -> None:
        """应用按钮样式"""
        style = ButtonStyles.get_style(self.button_type)
        self.setStyleSheet(style)
    
    def _on_clicked(self) -> None:
        """处理按钮点击"""
        if self.associated_data is not None:
            self.clicked_with_data.emit(self.associated_data)
    
    def set_button_type(self, button_type: str) -> None:
        """设置按钮类型
        
        Args:
            button_type: 新的按钮类型
        """
        self.button_type = button_type
        self._apply_style()
    
    def set_loading(self, loading: bool) -> None:
        """设置加载状态
        
        Args:
            loading: 是否为加载状态
        """
        self.is_loading = loading
        if loading:
            self.setText("⏳ 处理中...")
            self.setEnabled(False)
        else:
            self.setText(self.original_text)
            self.setEnabled(True)
    
    def set_text_with_icon(self, text: str, icon_text: str = "") -> None:
        """设置带图标的文本
        
        Args:
            text: 文本内容
            icon_text: 图标字符（emoji或Unicode字符）
        """
        if icon_text:
            self.setText(f"{icon_text} {text}")
        else:
            self.setText(text)
        self.original_text = self.text()
    
    def set_data(self, data: object) -> None:
        """设置关联数据
        
        Args:
            data: 要关联的数据
        """
        self.associated_data = data
    
    def flash_success(self, message: str = "成功!", duration: int = 2000) -> None:
        """显示成功状态的闪烁效果
        
        Args:
            message: 成功消息
            duration: 显示持续时间（毫秒）
        """
        original_type = self.button_type
        original_text = self.original_text
        
        # 临时改为成功样式
        self.set_button_type("success")
        self.setText(f"✅ {message}")
        
        # 设置定时器恢复原状
        QTimer.singleShot(duration, lambda: self._restore_original_state(original_type, original_text))
    
    def flash_error(self, message: str = "失败!", duration: int = 2000) -> None:
        """显示错误状态的闪烁效果
        
        Args:
            message: 错误消息
            duration: 显示持续时间（毫秒）
        """
        original_type = self.button_type
        original_text = self.original_text
        
        # 临时改为危险样式
        self.set_button_type("danger")
        self.setText(f"❌ {message}")
        
        # 设置定时器恢复原状
        QTimer.singleShot(duration, lambda: self._restore_original_state(original_type, original_text))
    
    def _restore_original_state(self, original_type: str, original_text: str) -> None:
        """恢复原始状态"""
        self.set_button_type(original_type)
        self.setText(original_text)
        self.original_text = original_text


class IconButton(ModernButton):
    """图标按钮（仅图标，无文字）"""
    
    def __init__(self, icon_text: str, button_type: str = "primary", tooltip: str = ""):
        """初始化图标按钮
        
        Args:
            icon_text: 图标字符
            button_type: 按钮类型
            tooltip: 工具提示
        """
        super().__init__(icon_text, button_type)
        
        # 设置为圆形按钮
        self._make_circular()
        
        # 设置工具提示
        if tooltip:
            self.setToolTip(tooltip)
    
    def _make_circular(self) -> None:
        """使按钮变为圆形"""
        # 设置固定大小
        self.setFixedSize(40, 40)
        
        # 添加圆形样式
        base_style = self.styleSheet()
        circular_style = base_style.replace(
            "border-radius: 8px;",
            "border-radius: 20px;"
        ).replace(
            "padding: 10px 20px;",
            "padding: 0px;"
        )
        self.setStyleSheet(circular_style)


class ButtonGroup:
    """按钮组管理器"""
    
    def __init__(self):
        """初始化按钮组"""
        self.buttons: list[ModernButton] = []
        self.selected_button: Optional[ModernButton] = None
    
    def add_button(self, button: ModernButton) -> None:
        """添加按钮到组中
        
        Args:
            button: 要添加的按钮
        """
        self.buttons.append(button)
        button.clicked.connect(lambda: self.select_button(button))
    
    def select_button(self, button: ModernButton) -> None:
        """选择指定按钮
        
        Args:
            button: 要选择的按钮
        """
        # 重置所有按钮
        for btn in self.buttons:
            btn.set_button_type("outline")
        
        # 设置选中按钮
        button.set_button_type("primary")
        self.selected_button = button
    
    def get_selected_button(self) -> Optional[ModernButton]:
        """获取当前选中的按钮
        
        Returns:
            当前选中的按钮，如果没有则返回None
        """
        return self.selected_button 