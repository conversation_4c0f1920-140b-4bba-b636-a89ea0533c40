"""
任务调度器 - 核心调度逻辑 (Pythonic 改进版本)
"""

import threading
import time
import importlib.util
import sys
import io
import contextlib
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any, Union
from concurrent.futures import Thread<PERSON>oolExecutor, Future
from enum import Enum
import logging

from .task_scanner import TaskScanner, TaskInfo
from .config_manager import ConfigManager
from .scheduler_config import SchedulerConfigManager
from .frequency_parser import FrequencyParser
from .task_logger import TaskLogger


class TaskStatus(Enum):
    """任务状态枚举"""
    WAITING = "等待中"
    RUNNING = "执行中" 
    DISABLED = "已禁用"


class TaskExecutionError(Exception):
    """任务执行异常"""
    pass


class SchedulerError(Exception):
    """调度器异常"""
    pass


class LogCapture:
    """日志捕获器 - 重定向print输出到日志文件
    
    采用单例模式确保全局只有一个活跃的捕获器
    """
    
    _global_lock = threading.Lock()
    _active_capture: Optional['LogCapture'] = None

    def __init__(self, log_file_path: Path, task_logger: TaskLogger) -> None:
        """初始化日志捕获器
        
        Args:
            log_file_path: 日志文件路径
            task_logger: 任务日志管理器
        """
        self.log_file_path = log_file_path
        self.task_logger = task_logger
        self.captured_output: Optional[io.StringIO] = None
        self.original_stdout = sys.stdout
        self.is_active = False

    def __enter__(self) -> 'LogCapture':
        """进入上下文管理器"""
        LogCapture._global_lock.acquire()

        try:
            if LogCapture._active_capture is not None:
                return self

            self.captured_output = io.StringIO()
            self.original_stdout = sys.stdout
            sys.stdout = self.captured_output
            LogCapture._active_capture = self
            self.is_active = True

        except Exception:
            LogCapture._global_lock.release()
            raise

        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """退出上下文管理器"""
        try:
            if self.is_active and LogCapture._active_capture is self:
                self._restore_stdout()
                self._write_captured_output()
                self._cleanup_resources()
                LogCapture._active_capture = None
                self.is_active = False

        except Exception:
            try:
                sys.stdout = self.original_stdout
                LogCapture._active_capture = None
            except Exception:
                pass
        finally:
            try:
                LogCapture._global_lock.release()
            except Exception:
                pass

    def _restore_stdout(self) -> None:
        """恢复标准输出"""
        sys.stdout = self.original_stdout

    def _write_captured_output(self) -> None:
        """写入捕获的输出到日志文件"""
        if not self.captured_output:
            return
            
        captured_text = self.captured_output.getvalue()
        if not captured_text.strip():
            return
            
        lines = captured_text.strip().split('\n')
        for line in lines:
            if line.strip() and self._should_log_line(line.strip()):
                try:
                    self.task_logger.write_log(
                        self.log_file_path, "TASK_OUTPUT", line.strip()
                    )
                except Exception:
                    pass  # 静默处理日志写入错误

    def _cleanup_resources(self) -> None:
        """清理资源"""
        if self.captured_output and not self.captured_output.closed:
            try:
                self.captured_output.close()
            except Exception:
                pass

    def _should_log_line(self, line: str) -> bool:
        """判断是否应该记录这行日志
        
        Args:
            line: 日志行内容
            
        Returns:
            是否应该记录
        """
        system_prefixes = [
            "💾 保存", "📋 发现", "✅ 所有任务配置", "🔄 任务状态更新",
            "🔄 任务完成触发UI刷新", "🔄 定时器触发UI刷新", "🚀 开始执行任务",
            "✅ 任务执行成功", "💾 保存调度器配置", "💾 保存任务配置", "💾 保存全局配置"
        ]
        
        return not any(line.startswith(prefix) for prefix in system_prefixes)


class TaskScheduler:
    """任务调度器
    
    负责管理和调度自动化任务的执行，支持并发执行和状态管理。
    """
    
    def __init__(self, 
                 task_scanner: TaskScanner,
                 config_manager: ConfigManager,
                 task_logger: TaskLogger,
                 status_callback: Optional[Callable[[str], None]] = None) -> None:
        """初始化调度器
        
        Args:
            task_scanner: 任务扫描器
            config_manager: 任务配置管理器
            task_logger: 任务日志管理器
            status_callback: 状态更新回调函数
        """
        self.task_scanner = task_scanner
        self.config_manager = config_manager
        self.task_logger = task_logger
        self.scheduler_config = SchedulerConfigManager()
        self.frequency_parser = FrequencyParser()
        self.status_callback = status_callback
        
        # 调度器状态
        self.is_scheduler_running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.executor: Optional[ThreadPoolExecutor] = None
        self.running_tasks: Dict[str, Future] = {}
        
        # 统计信息
        self.last_check_time: Optional[datetime] = None
        self.next_check_time: Optional[datetime] = None
        
        self._log_safely("✅ 任务调度器初始化完成")
    
    def start(self) -> bool:
        """启动调度器
        
        Returns:
            启动是否成功
        """
        if self.is_scheduler_running:
            self._log_safely("⚠️ 调度器已经在运行中")
            return False

        try:
            self._update_scheduler_config(enabled=True)
            self._initialize_thread_pool()
            self._start_scheduler_thread()
            
            max_workers = self.scheduler_config.get_config().max_concurrent
            self._log_safely(f"🚀 任务调度器已启动 (最大并发: {max_workers})")
            self._notify_status_change("调度器已启动")
            
            return True

        except Exception as e:
            self._log_safely(f"❌ 启动调度器失败: {e}")
            self.is_scheduler_running = False
            return False
    
    def stop(self) -> bool:
        """停止调度器
        
        Returns:
            停止是否成功
        """
        if not self.is_scheduler_running:
            self._log_safely("⚠️ 调度器未在运行")
            return False

        try:
            self.is_scheduler_running = False
            self._wait_for_running_tasks()
            self._shutdown_thread_pool()
            self._wait_for_scheduler_thread()
            self._update_scheduler_config(enabled=False)
            
            self._log_safely("⏹️ 任务调度器已停止")
            self._notify_status_change("调度器已停止")
            
            return True
            
        except Exception as e:
            self._log_safely(f"❌ 停止调度器失败: {e}")
            return False
    
    def execute_task_immediately(self, task_name: str) -> bool:
        """立即执行指定任务
        
        Args:
            task_name: 任务名称
            
        Returns:
            执行是否成功提交
        """
        try:
            target_task = self._find_task_by_name(task_name)
            if not target_task:
                self._log_safely(f"❌ 未找到任务: {task_name}")
                return False
            
            if self._is_task_running(task_name):
                self._log_safely(f"⚠️ 任务已在运行中: {task_name}")
                return False
            
            if not self.executor:
                self._log_safely("❌ 调度器未启动，无法执行任务")
                return False
                
            self._execute_task_async(target_task)
            return True
                
        except Exception as e:
            self._log_safely(f"❌ 立即执行任务失败: {task_name} - {e}")
            return False

    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态
        
        Returns:
            调度器状态信息
        """
        config = self.scheduler_config.get_config()
        
        return {
            'is_running': self.is_scheduler_running,
            'last_check_time': self._format_datetime(self.last_check_time),
            'next_check_time': self._format_datetime(self.next_check_time),
            'running_tasks_count': len(self.running_tasks),
            'running_tasks': list(self.running_tasks.keys()),
            'total_executed': config.total_executed,
            'total_failed': config.total_failed,
            'success_rate': self.scheduler_config.get_success_rate(),
            'check_interval': config.check_interval,
            'max_concurrent': config.max_concurrent,
        }

    # Private methods
    
    def _initialize_thread_pool(self) -> None:
        """初始化线程池"""
        max_workers = self.scheduler_config.get_config().max_concurrent
        self.executor = ThreadPoolExecutor(
            max_workers=max_workers, 
            thread_name_prefix="TaskExecutor"
        )

    def _start_scheduler_thread(self) -> None:
        """启动调度线程"""
        self.is_scheduler_running = True
        self.scheduler_thread = threading.Thread(
            target=self._scheduler_loop, 
            daemon=True
        )
        self.scheduler_thread.start()

    def _wait_for_running_tasks(self) -> None:
        """等待正在运行的任务完成"""
        if not self.running_tasks:
            return
            
        self._log_safely(f"⏳ 等待 {len(self.running_tasks)} 个任务完成...")
        for task_name, future in self.running_tasks.items():
            try:
                future.result(timeout=5)
            except Exception:
                self._log_safely(f"⚠️ 任务 {task_name} 未能及时完成")

    def _shutdown_thread_pool(self) -> None:
        """关闭线程池"""
        if self.executor:
            self.executor.shutdown(wait=True)
            self.executor = None

    def _wait_for_scheduler_thread(self) -> None:
        """等待调度线程结束"""
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)

    def _update_scheduler_config(self, enabled: bool) -> None:
        """更新调度器配置"""
        self.scheduler_config.set_scheduler_enabled(enabled)

    def _scheduler_loop(self) -> None:
        """调度器主循环"""
        self._log_safely("🔄 调度器循环开始")
        
        while self.is_scheduler_running:
            try:
                self._execute_scheduler_cycle()
                self._wait_for_next_cycle()
                
            except Exception as e:
                self._log_safely(f"❌ 调度器循环错误: {e}")
                time.sleep(10)  # 出错时等待10秒再继续
        
        self._log_safely("🔄 调度器循环结束")

    def _execute_scheduler_cycle(self) -> None:
        """执行一个调度周期"""
        current_time = datetime.now()
        self.last_check_time = current_time
        
        self._update_last_check_time(current_time)
        self._check_and_execute_tasks(current_time)
        self._cleanup_completed_tasks()
        self._calculate_next_check_time(current_time)

    def _update_last_check_time(self, current_time: datetime) -> None:
        """更新最后检查时间"""
        self.scheduler_config.update_last_check_time(
            current_time.strftime("%Y-%m-%d %H:%M:%S")
        )

    def _calculate_next_check_time(self, current_time: datetime) -> None:
        """计算下次检查时间"""
        check_interval = self.scheduler_config.get_config().check_interval
        self.next_check_time = current_time + timedelta(seconds=check_interval)
        self._notify_status_change(
            f"检查完成，下次检查: {self.next_check_time.strftime('%H:%M:%S')}"
        )

    def _wait_for_next_cycle(self) -> None:
        """等待下一个周期"""
        check_interval = self.scheduler_config.get_config().check_interval
        time.sleep(check_interval)

    def _check_and_execute_tasks(self, current_time: datetime) -> None:
        """检查并执行到期的任务
        
        Args:
            current_time: 当前时间
        """
        try:
            tasks = self.task_scanner.scan_tasks()
            
            for task in tasks:
                if self._should_execute_task(task, current_time):
                    self._execute_task_async(task)
                    
        except Exception as e:
            self._log_safely(f"❌ 检查任务时出错: {e}")

    def _should_execute_task(self, task: TaskInfo, current_time: datetime) -> bool:
        """判断是否应该执行任务
        
        Args:
            task: 任务信息
            current_time: 当前时间
            
        Returns:
            是否应该执行
        """
        config = self.config_manager.get_task_config(task.name)
        
        # 跳过禁用的任务
        if not config.is_enabled:
            return False
        
        # 跳过正在运行的任务
        if self._is_task_running(task.name):
            return False
        
        # 检查是否到了执行时间
        return (config.next_run_time and 
                self.frequency_parser.is_time_to_run(config.next_run_time, current_time))

    def _execute_task_async(self, task: TaskInfo) -> None:
        """异步执行任务
        
        Args:
            task: 任务信息
        """
        try:
            self._log_safely(f"🚀 开始执行任务: {task.name}")
            
            future = self.executor.submit(self._execute_task, task)
            self.running_tasks[task.name] = future
            
            self._update_task_status(task.name, TaskStatus.RUNNING)
            self._notify_status_change(f"开始执行任务: {task.name}")
            
        except Exception as e:
            self._log_safely(f"❌ 提交任务执行失败: {task.name} - {e}")

    def _execute_task(self, task: TaskInfo) -> None:
        """执行单个任务
        
        Args:
            task: 任务信息
        """
        log_file = None
        
        try:
            log_file = self._create_task_log(task.name)
            result = self._run_task_function(task, log_file)
            self._handle_successful_execution(task, log_file)
            
        except Exception as e:
            self._handle_failed_execution(task, log_file, e)
            
        finally:
            self._cleanup_task_execution(task.name)

    def _create_task_log(self, task_name: str) -> Path:
        """创建任务日志文件
        
        Args:
            task_name: 任务名称
            
        Returns:
            日志文件路径
        """
        return self.task_logger.create_log_file(task_name)

    def _run_task_function(self, task: TaskInfo, log_file: Path) -> Any:
        """运行任务函数
        
        Args:
            task: 任务信息
            log_file: 日志文件路径
            
        Returns:
            任务执行结果
            
        Raises:
            TaskExecutionError: 任务执行失败
        """
        try:
            module = self._load_task_module(task)
            func_to_call = self._get_task_function(module, task.file_path)
            return self._execute_task_function(func_to_call, log_file)
            
        except (ImportError, AttributeError) as e:
            raise TaskExecutionError(f"加载任务模块失败: {e}")

    def _load_task_module(self, task: TaskInfo):
        """加载任务模块
        
        Args:
            task: 任务信息
            
        Returns:
            加载的模块
            
        Raises:
            ImportError: 模块加载失败
        """
        spec = importlib.util.spec_from_file_location(task.name, task.file_path)
        if spec is None or spec.loader is None:
            raise ImportError(f"无法加载任务模块: {task.file_path}")

        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return module

    def _get_task_function(self, module, file_path: str) -> Callable:
        """获取任务执行函数
        
        Args:
            module: 任务模块
            file_path: 文件路径
            
        Returns:
            可执行的函数
            
        Raises:
            AttributeError: 找不到执行函数
        """
        for func_name in ['run', 'main']:
            if hasattr(module, func_name):
                return getattr(module, func_name)
        
        raise AttributeError(f"任务文件中未找到run()或main()函数: {file_path}")

    def _execute_task_function(self, func: Callable, log_file: Path) -> Any:
        """执行任务函数
        
        Args:
            func: 任务函数
            log_file: 日志文件路径
            
        Returns:
            执行结果
        """
        try:
            with LogCapture(log_file, self.task_logger):
                return func()
        except Exception as capture_error:
            self._log_safely(f"⚠️ 日志捕获失败，直接执行任务: {capture_error}")
            return func()

    def _handle_successful_execution(self, task: TaskInfo, log_file: Path) -> None:
        """处理成功执行的任务
        
        Args:
            task: 任务信息
            log_file: 日志文件路径
        """
        self.task_logger.finish_log(log_file, success=True)
        self.scheduler_config.increment_executed()
        
        self._log_safely(f"✅ 任务执行成功: {task.name}")
        self._notify_status_change(f"任务执行成功: {task.name}")

    def _handle_failed_execution(self, task: TaskInfo, log_file: Optional[Path], error: Exception) -> None:
        """处理失败执行的任务
        
        Args:
            task: 任务信息
            log_file: 日志文件路径
            error: 异常信息
        """
        error_msg = f"任务执行失败: {str(error)}"
        self._log_safely(f"❌ {task.name} - {error_msg}")
        
        if log_file:
            self.task_logger.write_log(log_file, "ERROR", error_msg)
            self.task_logger.finish_log(log_file, success=False, error_msg=str(error))
        
        self.scheduler_config.increment_failed()

    def _cleanup_task_execution(self, task_name: str) -> None:
        """清理任务执行后的状态
        
        Args:
            task_name: 任务名称
        """
        self._update_task_after_execution(task_name)
        
        if task_name in self.running_tasks:
            del self.running_tasks[task_name]

    def _update_task_after_execution(self, task_name: str) -> None:
        """任务执行后更新配置
        
        Args:
            task_name: 任务名称
        """
        try:
            self.config_manager.increment_run_count(task_name)
            self._update_next_run_time(task_name)
            self._update_task_status(task_name, TaskStatus.WAITING)
            
            self._log_safely(f"🔄 任务状态更新: {task_name} → 等待中")
            self._notify_status_change(f"任务完成，状态恢复: {task_name} → 等待中")
            
        except Exception as e:
            self._log_safely(f"❌ 更新任务配置失败: {task_name} - {e}")

    def _update_next_run_time(self, task_name: str) -> None:
        """更新下次运行时间
        
        Args:
            task_name: 任务名称
        """
        config = self.config_manager.get_task_config(task_name)
        if config.frequency_minutes > 0:
            next_run_time = self.frequency_parser.calculate_next_run_time(
                config.frequency_minutes, 
                config.last_run_time
            )
            self.config_manager.update_task_config(
                task_name, 
                next_run_time=next_run_time
            )

    def _cleanup_completed_tasks(self) -> None:
        """清理已完成的任务"""
        completed_tasks = [
            task_name for task_name, future in self.running_tasks.items()
            if future.done()
        ]
        
        for task_name in completed_tasks:
            del self.running_tasks[task_name]

    def _find_task_by_name(self, task_name: str) -> Optional[TaskInfo]:
        """根据名称查找任务
        
        Args:
            task_name: 任务名称
            
        Returns:
            任务信息，未找到则返回None
        """
        tasks = self.task_scanner.scan_tasks()
        return next((task for task in tasks if task.name == task_name), None)

    def _is_task_running(self, task_name: str) -> bool:
        """检查任务是否正在运行
        
        Args:
            task_name: 任务名称
            
        Returns:
            是否正在运行
        """
        return task_name in self.running_tasks

    def _update_task_status(self, task_name: str, status: TaskStatus) -> None:
        """更新任务状态
        
        Args:
            task_name: 任务名称
            status: 新状态
        """
        self.config_manager.set_task_status(task_name, status.value)
        self._log_safely(f"🔄 任务状态更新: {task_name} → {status.value}")

    def _notify_status_change(self, message: str) -> None:
        """通知状态变化
        
        Args:
            message: 状态消息
        """
        if self.status_callback:
            try:
                self.status_callback(message)
            except Exception as e:
                self._log_safely(f"❌ 状态回调失败: {e}")

    def _log_safely(self, message: str) -> None:
        """安全的日志记录方法
        
        Args:
            message: 日志消息
        """
        try:
            print(message)
        except (ValueError, OSError):
            pass  # 静默忽略print失败

    def _format_datetime(self, dt: Optional[datetime]) -> str:
        """格式化日期时间
        
        Args:
            dt: 日期时间对象
            
        Returns:
            格式化后的字符串
        """
        return dt.strftime("%Y-%m-%d %H:%M:%S") if dt else "" 