{"database": {"mysql": {"host": "*************", "port": 3306, "username": "testuser", "password": "", "database": "", "charset": "utf8mb4"}, "sqlite": {"path": "userdata/database/app.db", "timeout": 30}, "redis": {"host": "localhost", "port": 6379, "password": "", "db": 0}}, "api": {"weather": {"url": "https://api.openweathermap.org/data/2.5/weather", "key": "your-api-key-here", "timeout": 30}, "news": {"url": "https://newsapi.org/v2/top-headlines", "key": "", "timeout": 30}}, "files": {"upload_dir": "userdata/files/uploads", "download_dir": "userdata/files/downloads", "temp_dir": "userdata/cache/temp", "max_file_size": 10485760}, "system": {"debug": false, "log_level": "INFO", "timezone": "Asia/Shanghai", "language": "zh-CN"}, "custom": {"user_name": "演示用户", "email": "<EMAIL>", "phone": "", "app_name": "任务调度系统", "version": "1.0.0", "last_login": "2025-07-02 21:44:44"}, "test": {"deep": {"nested": {"value": "深层配置"}}}, "demo_task": {"name": "全局配置演示", "version": "1.0.0", "settings": {"auto_save": true, "max_retries": 3, "timeout": 30}}, "app": {"features": {"notifications": {"email": true, "sms": false}, "backup": {"enabled": true, "interval": 24}}, "mode": "night", "theme": "dark"}, "task_stats": {"global_config_demo": {"run_count": 1, "last_run": "2025-07-02 21:44:44", "status": "success"}}}