#!/usr/bin/env python3
"""
状态监控任务 - 30秒间隔执行，监控系统状态和其他任务执行情况
"""

# 任务名称: 状态监控任务
# 描述: 每30秒执行一次，监控系统状态和任务执行统计
# 作者: 测试
# 版本: 1.0.0
# 分类: 监控

import sys
import os
import time
import psutil
from datetime import datetime

# 导入全局配置
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared.global_config import G


def run():
    """任务执行函数"""
    start_time = datetime.now()
    print(f"📊 状态监控任务开始执行 - {start_time.strftime('%H:%M:%S')}")
    
    # 记录任务统计
    current_count = G.get("test_stats.monitor_task.run_count", 0)
    G.test_stats.monitor_task.run_count = current_count + 1
    G.test_stats.monitor_task.last_run = start_time.strftime("%Y-%m-%d %H:%M:%S")
    
    print(f"🔍 第 {G.test_stats.monitor_task.run_count} 次监控检查")
    
    # 收集系统信息
    try:
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        system_info = {
            "cpu_percent": round(cpu_percent, 1),
            "memory_percent": round(memory.percent, 1),
            "memory_available_gb": round(memory.available / (1024**3), 2),
            "disk_percent": round(disk.percent, 1),
            "disk_free_gb": round(disk.free / (1024**3), 2)
        }
        
        print(f"💻 系统状态:")
        print(f"  CPU使用率: {system_info['cpu_percent']}%")
        print(f"  内存使用率: {system_info['memory_percent']}% (可用: {system_info['memory_available_gb']}GB)")
        print(f"  磁盘使用率: {system_info['disk_percent']}% (可用: {system_info['disk_free_gb']}GB)")
        
    except Exception as e:
        print(f"⚠️ 无法获取系统信息: {e}")
        system_info = {"error": str(e)}
    
    # 监控其他任务的执行统计
    print(f"\n📈 任务执行统计:")
    
    task_stats = {}
    for task_name in ["quick_task", "medium_task", "long_task"]:
        stats_key = f"test_stats.{task_name}"
        run_count = G.get(f"{stats_key}.run_count", 0)
        last_run = G.get(f"{stats_key}.last_run", "从未执行")
        status = G.get(f"{stats_key}.status", "未知")
        
        task_stats[task_name] = {
            "run_count": run_count,
            "last_run": last_run,
            "status": status
        }
        
        print(f"  {task_name}: 执行{run_count}次, 状态:{status}, 最后执行:{last_run}")
    
    # 计算总体统计
    total_executions = sum(stats["run_count"] for stats in task_stats.values())
    active_tasks = sum(1 for stats in task_stats.values() if stats["run_count"] > 0)
    
    print(f"\n📊 总体统计:")
    print(f"  活跃任务数: {active_tasks}/3")
    print(f"  总执行次数: {total_executions}")
    print(f"  监控运行次数: {G.test_stats.monitor_task.run_count}")
    
    # 保存监控数据
    G.test_stats.monitor_task.last_system_info = system_info
    G.test_stats.monitor_task.last_task_stats = task_stats
    G.test_stats.monitor_task.total_executions_monitored = total_executions
    G.test_stats.monitor_task.status = "success"
    
    # 记录完成时间
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    G.test_stats.monitor_task.last_duration = duration
    
    print(f"✅ 状态监控完成 - 耗时: {duration:.1f}秒")
    
    return {
        "status": "success",
        "duration": duration,
        "run_count": G.test_stats.monitor_task.run_count,
        "system_info": system_info,
        "task_stats": task_stats,
        "total_executions": total_executions,
        "active_tasks": active_tasks,
        "message": f"监控完成，发现 {active_tasks} 个活跃任务，总执行 {total_executions} 次"
    }


if __name__ == "__main__":
    result = run()
    print(f"📋 监控结果: {result}")
