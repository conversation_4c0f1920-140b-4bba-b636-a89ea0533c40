"""
目录管理器 - 统一管理系统目录结构
"""

import os
import json
from pathlib import Path
from typing import Dict, Any


class DirectoryManager:
    """目录管理器"""
    
    def __init__(self, base_dir: str = "."):
        self.base_dir = Path(base_dir)
        self.setup_directories()
    
    def setup_directories(self) -> None:
        """设置目录结构"""
        # 创建主要目录
        directories = {
            'config': self.base_dir / 'config',      # 配置文件目录
            'logs': self.base_dir / 'logs',          # 日志目录
            'tasks': self.base_dir / 'tasks',        # 任务目录
        }
        
        for name, path in directories.items():
            path.mkdir(exist_ok=True)
            print(f"📁 确保目录存在: {path}")

        # 创建说明文件
        self.create_readme_files()
    
    def get_config_dir(self) -> Path:
        """获取配置目录"""
        return self.base_dir / 'config'
    

    
    def get_logs_dir(self) -> Path:
        """获取日志目录"""
        return self.base_dir / 'logs'
    
    def get_tasks_dir(self) -> Path:
        """获取任务目录"""
        return self.base_dir / 'tasks'
    
    def get_config_file(self, filename: str) -> Path:
        """获取配置文件路径"""
        return self.get_config_dir() / filename
    

    
    def create_readme_files(self) -> None:
        """创建说明文件"""
        # 配置目录说明
        config_readme = self.get_config_dir() / "README.md"
        if not config_readme.exists():
            with open(config_readme, 'w', encoding='utf-8') as f:
                f.write("""# 配置文件目录

此目录包含系统的所有配置文件。

## 文件说明

- `task_configs.json` - 任务配置文件
- `scheduler_config.json` - 调度器配置文件

## 注意事项

- 请勿手动编辑配置文件，除非您知道自己在做什么
- 建议在修改前备份配置文件
- 配置文件使用UTF-8编码

## 全局配置

用户数据配置文件 `global_config.json` 位于项目根目录，可以通过任务中的 `G.xx` 方式访问。
""")
    

    
    def migrate_old_configs(self) -> None:
        """迁移旧的配置文件到新目录"""
        old_configs = [
            ('task_configs.json', 'task_configs.json'),
            ('scheduler_config.json', 'scheduler_config.json'),
        ]
        
        for old_file, new_file in old_configs:
            old_path = self.base_dir / old_file
            new_path = self.get_config_file(new_file)
            
            if old_path.exists() and not new_path.exists():
                try:
                    # 复制文件到新位置
                    import shutil
                    shutil.copy2(old_path, new_path)
                    print(f"📦 迁移配置文件: {old_file} → {new_path}")
                    
                    # 删除旧文件
                    old_path.unlink()
                    print(f"🗑️ 删除旧配置文件: {old_path}")
                    
                except Exception as e:
                    print(f"❌ 迁移配置文件失败: {e}")


if __name__ == "__main__":
    # 测试目录管理器
    manager = DirectoryManager()
    
    print("\n📁 目录结构:")
    print(f"  配置目录: {manager.get_config_dir()}")
    print(f"  用户数据目录: {manager.get_userdata_dir()}")
    print(f"  日志目录: {manager.get_logs_dir()}")
    print(f"  任务目录: {manager.get_tasks_dir()}")
    
    # 创建默认配置
    manager.create_default_userdata_configs()
    
    # 测试配置加载
    db_config = manager.load_userdata_config('database', 'mysql.json')
    print(f"\n📄 MySQL配置: {db_config}")
    
    print("\n✅ 目录管理器测试完成")
