"""
表格样式常量模块
提供统一的表格样式定义
"""


class TableStyles:
    """任务表格样式常量类"""
    
    # 基础表格样式
    MAIN_TABLE = """
        QTableWidget {
            gridline-color: #e5e7eb;
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            selection-background-color: #eff6ff;
        }
        QTableWidget::item {
            padding: 12px;
            border-bottom: 1px solid #f3f4f6;
        }
        QTableWidget::item:selected {
            background-color: #eff6ff;
            color: #1e40af;
        }
        QTableWidget::item:hover {
            background-color: #f8fafc;
        }
    """
    
    # 表头样式
    HEADER = """
        QHeaderView::section {
            background-color: #f9fafb;
            padding: 12px;
            border: none;
            border-bottom: 2px solid #e5e7eb;
            font-weight: 600;
            color: #374151;
            text-align: left;
        }
        QHeaderView::section:hover {
            background-color: #f3f4f6;
        }
    """
    
    # 行号样式
    VERTICAL_HEADER = """
        QHeaderView::section {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            padding: 8px;
            font-weight: 600;
            color: #374151;
            text-align: center;
            min-width: 40px;
        }
    """
    
    # 状态单元格样式
    STATUS_RUNNING = """
        background-color: #dcfce7;
        color: #166534;
        font-weight: 600;
        border-radius: 4px;
        padding: 4px 8px;
    """
    
    STATUS_WAITING = """
        background-color: #dbeafe;
        color: #1e40af;
        font-weight: 600;
        border-radius: 4px;
        padding: 4px 8px;
    """
    
    STATUS_DISABLED = """
        background-color: #fef2f2;
        color: #991b1b;
        font-weight: 600;
        border-radius: 4px;
        padding: 4px 8px;
    """
    
    @classmethod
    def get_complete_style(cls) -> str:
        """获取完整的表格样式
        
        Returns:
            完整的CSS样式字符串
        """
        return cls.MAIN_TABLE + cls.HEADER
    
    @classmethod
    def get_status_style(cls, status: str) -> str:
        """获取状态对应的样式
        
        Args:
            status: 状态字符串
            
        Returns:
            对应的CSS样式字符串
        """
        status_map = {
            "执行中": cls.STATUS_RUNNING,
            "等待中": cls.STATUS_WAITING,
            "已禁用": cls.STATUS_DISABLED,
        }
        return status_map.get(status, cls.STATUS_WAITING) 