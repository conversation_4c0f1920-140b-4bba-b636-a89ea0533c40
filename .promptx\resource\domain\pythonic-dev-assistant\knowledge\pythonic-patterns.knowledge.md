# Pythonic编程模式知识库

## 数据库操作命名规范

### CRUD操作标准命名
```python
# 创建记录
def create_user(user_data: dict) -> User:
    """创建新用户"""
    pass

def create_order(order_info: dict) -> Order:
    """创建新订单"""
    pass

# 获取单个记录
def get_user_by_id(user_id: int) -> User:
    """根据ID获取用户"""
    pass

def get_product(product_id: str) -> Product:
    """获取产品信息"""
    pass

# 获取多个记录
def list_active_users() -> List[User]:
    """获取活跃用户列表"""
    pass

def list_orders(status: str = None) -> List[Order]:
    """获取订单列表"""
    pass

# 获取所有记录
def get_all_products() -> List[Product]:
    """获取所有产品"""
    pass

# 更新操作
def update_profile(user_id: int, profile_data: dict) -> bool:
    """更新用户资料"""
    pass

def update_order_status(order_id: str, status: str) -> bool:
    """更新订单状态"""
    pass

# 删除操作
def delete_user(user_id: int) -> bool:
    """删除用户"""
    pass

def delete_inactive_records() -> int:
    """删除非活跃记录"""
    pass

# 存在检查
def has_order(user_id: int) -> bool:
    """检查用户是否有订单"""
    pass

def has_permission(user_id: int, permission: str) -> bool:
    """检查用户权限"""
    pass

# 数量统计
def count_users() -> int:
    """统计用户数量"""
    pass

def count_active_sessions() -> int:
    """统计活跃会话数"""
    pass
```

## API开发命名规范

### RESTful API模式
```python
# API端点函数
def get_user(user_id: int) -> dict:
    """获取用户信息API"""
    pass

def create_order(order_payload: dict) -> dict:
    """创建订单API"""
    pass

# 查询参数处理
def list_products(filter_by_category: str = None, 
                 sort_by_price: bool = False) -> dict:
    """产品列表API"""
    pass

# API客户端
class GitHubClient:
    """GitHub API客户端"""
    
    def __init__(self, api_token: str):
        self.api_token = api_token
    
    def get_repository(self, repo_name: str) -> dict:
        """获取仓库信息"""
        pass

# 错误处理
class APIError(Exception):
    """API错误基类"""
    pass

def handle_api_error(error: APIError) -> dict:
    """处理API错误"""
    pass
```

## 文件处理命名规范

### 文件操作模式
```python
# 文件读取
def read_csv(file_path: str) -> List[dict]:
    """读取CSV文件"""
    pass

def read_config_file(config_path: str) -> dict:
    """读取配置文件"""
    pass

# 文件写入
def write_log(message: str, log_file: str) -> None:
    """写入日志"""
    pass

def write_report(data: dict, output_path: str) -> None:
    """写入报告"""
    pass

# 数据加载和保存
def load_dataset(data_path: str) -> pd.DataFrame:
    """加载数据集"""
    pass

def save_results(results: dict, output_file: str) -> None:
    """保存结果"""
    pass

# 文件处理
def process_image(image_path: str) -> str:
    """处理图片"""
    pass

def process_upload(uploaded_file) -> dict:
    """处理上传文件"""
    pass

# 路径操作
def get_file_path(filename: str) -> str:
    """获取文件路径"""
    pass

def ensure_dir_exists(dir_path: str) -> None:
    """确保目录存在"""
    pass
```

## 测试代码命名规范

### 测试模式
```python
import pytest
from unittest.mock import Mock, patch

class TestUserService:
    """用户服务测试类"""
    
    def test_create_user_success(self):
        """测试成功创建用户"""
        pass
    
    def test_create_user_invalid_email(self):
        """测试无效邮箱创建用户"""
        pass
    
    def test_get_user_by_id_not_found(self):
        """测试获取不存在的用户"""
        pass

# 测试数据
@pytest.fixture
def test_data_users():
    """测试用户数据"""
    return [
        {"id": 1, "name": "张三", "email": "<EMAIL>"},
        {"id": 2, "name": "李四", "email": "<EMAIL>"},
    ]

# 模拟对象
@pytest.fixture
def mock_database():
    """模拟数据库"""
    return Mock()

# 参数化测试
@pytest.mark.parametrize("param_user_data,expected", [
    ({"name": "张三", "email": "<EMAIL>"}, True),
    ({"name": "", "email": "<EMAIL>"}, False),
    ({"name": "张三", "email": "invalid-email"}, False),
])
def test_validate_user_data(param_user_data, expected):
    """参数化测试用户数据验证"""
    pass

# 性能测试
def perf_test_large_dataset():
    """性能测试大数据集处理"""
    pass

# 集成测试
def itest_order_workflow():
    """集成测试订单工作流"""
    pass
```

## 高级Pythonic模式

### 上下文管理器
```python
# 自定义上下文管理器
class DatabaseConnection:
    """数据库连接上下文管理器"""
    
    def __enter__(self):
        self.connection = create_connection()
        return self.connection
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.connection.close()

# 使用示例
with DatabaseConnection() as conn:
    result = conn.execute("SELECT * FROM users")
```

### 装饰器模式
```python
from functools import wraps
import time

def timing_decorator(func):
    """计时装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"{func.__name__} 执行时间: {end_time - start_time:.2f}秒")
        return result
    return wrapper

@timing_decorator
def slow_function():
    """慢函数示例"""
    time.sleep(1)
    return "完成"
```

### 生成器模式
```python
def read_large_file(file_path: str):
    """读取大文件的生成器"""
    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            yield line.strip()

# 使用示例
for line in read_large_file('large_file.txt'):
    process_line(line)
```

### 数据类模式
```python
from dataclasses import dataclass
from typing import Optional

@dataclass
class User:
    """用户数据类"""
    id: int
    name: str
    email: str
    is_active: bool = True
    age: Optional[int] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.age is not None and self.age < 0:
            raise ValueError("年龄不能为负数")
```

## 错误处理最佳实践

### 异常处理模式
```python
# 具体异常类型
try:
    result = risky_operation()
except FileNotFoundError:
    logger.error("文件未找到")
    return None
except PermissionError:
    logger.error("权限不足")
    return None
except Exception as e:
    logger.error(f"未知错误: {e}")
    raise

# 自定义异常
class ValidationError(Exception):
    """验证错误"""
    pass

class BusinessLogicError(Exception):
    """业务逻辑错误"""
    pass

def validate_user_input(data: dict) -> None:
    """验证用户输入"""
    if not data.get('email'):
        raise ValidationError("邮箱不能为空")
    
    if '@' not in data['email']:
        raise ValidationError("邮箱格式无效")
```

## 性能优化模式

### 字符串处理优化
```python
# ✅ 使用join()连接字符串
items = ['apple', 'banana', 'cherry']
result = ', '.join(items)

# ✅ 使用f-string格式化
name = "张三"
age = 25
message = f"用户{name}，年龄{age}岁"

# ✅ 避免重复字符串操作
def build_sql_query(conditions: List[str]) -> str:
    """构建SQL查询"""
    base_query = "SELECT * FROM users"
    if conditions:
        where_clause = " AND ".join(conditions)
        return f"{base_query} WHERE {where_clause}"
    return base_query
```

### 集合操作优化
```python
# ✅ 使用集合进行快速查找
valid_ids = {1, 2, 3, 4, 5}
if user_id in valid_ids:  # O(1) 时间复杂度
    process_user(user_id)

# ✅ 使用字典进行映射
status_mapping = {
    'active': '活跃',
    'inactive': '非活跃',
    'pending': '待审核',
}
chinese_status = status_mapping.get(status, '未知')
```
