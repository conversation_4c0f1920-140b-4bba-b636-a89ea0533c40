"""
测试表格点击功能的简单脚本
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QTableWidget, QTableWidgetItem, QVBoxLayout, QWidget, QTextEdit
from PyQt6.QtCore import Qt

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("表格点击测试")
        self.setGeometry(100, 100, 600, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建表格
        self.table = QTableWidget(3, 2)
        self.table.setHorizontalHeaderLabels(["任务名称", "状态"])
        
        # 禁用选中效果但保留点击事件
        self.table.setSelectionMode(QTableWidget.SelectionMode.NoSelection)
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        
        # 添加测试数据
        self.table.setItem(0, 0, QTableWidgetItem("文件整理"))
        self.table.setItem(0, 1, QTableWidgetItem("等待中"))
        self.table.setItem(1, 0, QTableWidgetItem("数据采集"))
        self.table.setItem(1, 1, QTableWidgetItem("执行中"))
        self.table.setItem(2, 0, QTableWidgetItem("示例任务"))
        self.table.setItem(2, 1, QTableWidgetItem("已禁用"))
        
        # 连接点击事件
        self.table.itemClicked.connect(self.on_item_clicked)
        
        # 创建日志显示
        self.log = QTextEdit()
        self.log.setMaximumHeight(150)
        
        layout.addWidget(self.table)
        layout.addWidget(self.log)
        
        self.log.append("点击表格行查看选择效果...")
    
    def on_item_clicked(self, item):
        """处理表格项点击事件"""
        if item is None:
            return
        
        row = item.row()
        task_name = self.table.item(row, 0).text()
        status = self.table.item(row, 1).text()
        
        self.log.append(f"📋 选中任务: {task_name} (状态: {status})")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec())
