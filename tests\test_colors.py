"""
测试颜色效果的简单脚本
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QTableWidget, QTableWidgetItem, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor, QFont

class ColorTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("颜色效果测试")
        self.setGeometry(100, 100, 800, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建按钮测试区域
        button_layout = QHBoxLayout()
        
        # 启动按钮（绿色）
        self.start_btn = QPushButton("▶️ 启动调度")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                           stop: 0 #22c55e, stop: 1 #16a34a);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: 600;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                           stop: 0 #16a34a, stop: 1 #15803d);
            }
        """)
        
        # 停止按钮（红色）
        self.stop_btn = QPushButton("⏹️ 停止调度")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                           stop: 0 #ef4444, stop: 1 #dc2626);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: 600;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                           stop: 0 #dc2626, stop: 1 #b91c1c);
            }
        """)
        
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 创建表格测试区域
        self.table = QTableWidget(3, 3)
        self.table.setHorizontalHeaderLabels(["任务名称", "状态", "描述"])
        
        # 添加测试数据
        tasks = [
            ("文件整理", "🟢 执行中", "正在整理文件"),
            ("数据采集", "🔵 等待中", "等待下次执行"),
            ("系统清理", "🔴 已禁用", "任务已禁用")
        ]
        
        for row, (name, status, desc) in enumerate(tasks):
            # 任务名称
            self.table.setItem(row, 0, QTableWidgetItem(name))
            
            # 状态（带颜色）
            status_item = QTableWidgetItem(status)
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # 设置字体
            font = QFont()
            font.setBold(True)
            font.setPointSize(9)
            status_item.setFont(font)
            
            if "执行中" in status:
                status_item.setBackground(QColor("#dcfce7"))
                status_item.setForeground(QColor("#166534"))
            elif "已禁用" in status:
                status_item.setBackground(QColor("#fef2f2"))
                status_item.setForeground(QColor("#991b1b"))
            else:  # 等待中
                status_item.setBackground(QColor("#dbeafe"))
                status_item.setForeground(QColor("#1e40af"))
            
            self.table.setItem(row, 1, status_item)
            
            # 描述
            self.table.setItem(row, 2, QTableWidgetItem(desc))
        
        # 设置表格属性
        self.table.setSelectionMode(QTableWidget.SelectionMode.NoSelection)
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.table.horizontalHeader().setStretchLastSection(True)
        
        layout.addWidget(self.table)
        
        # 按钮事件
        self.start_btn.clicked.connect(lambda: print("启动调度器"))
        self.stop_btn.clicked.connect(lambda: print("停止调度器"))

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ColorTestWindow()
    window.show()
    sys.exit(app.exec())
