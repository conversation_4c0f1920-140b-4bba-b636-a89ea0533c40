# Python标准规范知识体系

## PEP8核心规范

### 命名约定
```python
# 变量和函数：snake_case
user_name = "张三"
def calculate_total_price():
    pass

# 类名：PascalCase  
class UserManager:
    pass

# 常量：UPPER_CASE
MAX_RETRY_COUNT = 3

# 私有成员：前缀下划线
class MyClass:
    def __init__(self):
        self._private_var = "私有变量"
        self.__very_private = "强私有变量"
```

### 布尔值命名规范
```python
# 状态检查
is_active = True
is_valid = False

# 资源存在
has_permission = True
has_file = False

# 操作需求
needs_update = True
needs_backup = False

# 行为建议
should_retry = True
should_warn = False

# 可执行状态
can_edit = True
can_delete = False

# 已完成状态
was_processed = True
was_validated = False
```

### 代码格式规范
```python
# 行长度：不超过79字符
# 缩进：4个空格
# 空行：顶级函数和类之间2个空行，类内方法之间1个空行

class ExampleClass:
    """示例类"""
    
    def __init__(self):
        self.value = 0
    
    def method_one(self):
        """第一个方法"""
        pass
    
    def method_two(self):
        """第二个方法"""
        pass


def top_level_function():
    """顶级函数"""
    pass


def another_function():
    """另一个顶级函数"""
    pass
```

## Pythonic编程模式

### 列表推导式规范
```python
# ✅ 好的做法 - 简单推导式
squares = [x**2 for x in range(10)]
even_numbers = [x for x in range(20) if x % 2 == 0]

# ❌ 避免 - 复杂嵌套推导式
# 改用循环更清晰
result = []
for outer in data:
    for inner in outer:
        if condition(inner):
            result.append(process(inner))
```

### 内置函数优先使用
```python
# ✅ 使用all()检查所有元素
if all(item.is_valid for item in items):
    process_items()

# ✅ 使用any()检查至少一个元素
if any(item.needs_update for item in items):
    update_items()

# ✅ 使用enumerate()而非range(len())
for i, item in enumerate(items):
    print(f"{i}: {item}")

# ✅ 使用zip()处理多个序列
for name, age in zip(names, ages):
    print(f"{name} is {age} years old")
```

### 下划线的使用
```python
# ✅ 忽略不需要的值
for _ in range(10):
    print("Hello")

# ✅ 解包时忽略不需要的值
first, _, last = name_parts

# ✅ 忽略循环变量
for _, value in dictionary.items():
    process(value)
```

### 列表逗号使用规范
```python
# ✅ 单行列表 - 不要尾随逗号
colors = ['red', 'green', 'blue']

# ✅ 多行列表 - 推荐尾随逗号
long_list = [
    'item1',
    'item2',
    'item3',
]

# ✅ 多行字典 - 推荐尾随逗号
config = {
    'host': 'localhost',
    'port': 8080,
    'debug': True,
}
```

## 文档字符串规范

### 标准格式
```python
def read_lines_for_file(file_path: str) -> list:
    """read_lines_for_file 从文件读取行列表，并且过滤空行和换行符
    
    如果还有其他描述继续这里写
    如果有api文档写在这里
    
    Note:
        需要注意的关键点
        
    Args:
        file_path: 文件路径参数说明
        
    Returns:
        返回过滤后的行列表
        
    Raises:
        FileNotFoundError: 文件不存在时抛出
        PermissionError: 权限不足时抛出
    """
    pass
```

## 类设计最佳实践

### 理想的类结构
```python
class YXFCollection:
    # 类属性(全局统计，默认变量值，常量等有业务意义的数字)
    collection_count = 0
    max_number_of_retries = 3
    
    # 构造方法，用于初始化
    def __init__(self, username: str, password: str) -> None:
        # 实例属性 每个实例独有的，不需要多实例之间共享
        self.username = username
        self.password = password
    
    # 特殊的方法（魔术方法）
    def __str__(self) -> str:
        """__str__ 友好显示（最有用的一个）
        让 print(bot) 显示有意义的信息，调试时很方便
        """
        return f'执行的用户名：{self.username}'
    
    # 类方法(类属性读写，不实例)
    @classmethod
    def get_execute_data(cls) -> dict:
        """get_execute_data 获取类属性执行数据"""
        return {
            "collection_count": cls.collection_count,
            "max_number_of_retries": cls.max_number_of_retries,
        }
    
    # 静态方法（独立的纯工具函数）不依赖于实例
    @staticmethod
    def validate_url(url: str) -> bool:
        """静态方法：验证URL格式"""
        return url.startswith(('http://', 'https://')) and len(url) > 10
    
    # 实例方法_私有方法
    def _handle_product_data(self, product_data: dict) -> dict:
        """将采集的原始数据处理为规整的字典"""
        return product_data
    
    # 实例方法_公有方法
    def collection_product(self, url: str) -> dict:
        """采集商品数据"""
        return {}
```

### 类的封装原则

#### 设计原则
- 对外暴露接口越少越好，具体实现在类内部
- 每个方法单一职责，干一件事，最好不超过20行
- 重复的或独立的功能判断可以委托其他方法去完成
- 具体实例方法位置，按顺序放在最近的地方，类似于从主函数发散出
- 内部调用的第一个方法，在这个函数对应上部位置

#### 什么是过度封装？
- **已经达到职责单一**，但为了所谓的架构（将来 - 也许没有的将来的拓展）凭经验做了过多的技术层面的封装，和业务的关系不大，仅仅为了灵活性、扩展性（本可在迭代阶段重构）、语言特性等的动机做出了过多的设计
- **已经高内聚，低耦合**了，而且方法或者依赖已经能够达到业务上的清晰描述和行为互动了，但是却又把同一业务意义的功能粒度再度细化，导致本来很清晰的业务流程掺入不应该有的干扰因素（需要维护者花精力去组合才明白业务意义）

#### 函数 vs 类的选择原则
- **使用类**：有状态、有属性、需要方法的"有血有肉"情况，有一些统一的属性状态管理的情况下采用
- **使用函数**：无状态、无记忆、用完即可抛弃的情况用函数，一些通用的工具类函数没有必要用类去封装
- **同领域相关功能**：可考虑用类聚合起来
- **数据类使用**：一般不用，除非是有固定字段的商品数据或者用户数据之类的

## 完整命名规范体系

### 基本原则
- **清晰性**：名称应清晰表达意图
- **一致性**：在整个项目中保持相同的命名风格
- **简洁性**：在清晰的前提下保持简洁
- **Python惯例**：遵循PEP8和社区共识

### 布尔值标记命名规范
| 使用场景 | 推荐前缀 | 示例 | 说明 |
|---|---|---|---|
| 状态检查 | `is_` | `is_active`, `is_valid` | 表示对象或值是否处于某种状态 |
| 资源存在 | `has_` | `has_permission`, `has_file` | 表示是否拥有某种资源或属性 |
| 操作需求 | `needs_` | `needs_update`, `needs_backup` | 表示是否需要执行某个操作 |
| 行为建议 | `should_` | `should_retry`, `should_warn` | 表示是否应该执行某个行为 |
| 可执行状态 | `can_` | `can_edit`, `can_delete` | 表示是否具有执行某个操作的能力 |
| 已完成状态 | `was_` | `was_processed`, `was_validated` | 表示某个操作是否已完成 |

### 数据库操作命名规范
| 操作类型 | 推荐命名 | 示例 | 说明 |
|---|---|---|---|
| 创建记录 | `create_` | `create_user()`, `create_order()` | 创建新的数据库记录 |
| 获取单个 | `get_` | `get_user_by_id()`, `get_product()` | 获取单个数据库记录 |
| 获取多个 | `list_` | `list_active_users()`, `list_orders()` | 获取多个数据库记录 |
| 获取所有 | `get_all_` | `get_all_products()`, `get_all_customers()` | 获取所有数据库记录 |
| 更新操作 | `update_` | `update_profile()`, `update_order_status()` | 更新现有数据库记录 |
| 删除操作 | `delete_` | `delete_user()`, `delete_inactive_records()` | 删除数据库记录 |
| 存在检查 | `has_` | `has_order()`, `has_permission()` | 检查记录是否存在 |
| 数量统计 | `count_` | `count_users()`, `count_active_sessions()` | 统计记录数量 |
| 复杂查询 | `find_` | `find_users_by_location()`, `find_orders_in_date_range()` | 复杂条件查询 |
| 聚合操作 | `aggregate_` | `aggregate_sales_data()`, `aggregate_monthly_totals()` | 聚合查询计算 |
| 分页查询 | `paginate_` | `paginate_users()`, `paginate_results()` | 分页查询记录 |

### API开发命名规范
| 组件类型 | 推荐命名 | 示例 | 说明 |
|---|---|---|---|
| RESTful端点 | 资源名 | `/api/users`, `/api/orders` | API资源端点 |
| 路径参数 | 资源ID | `user_id`, `order_id` | URL中的资源标识符 |
| 查询参数 | `filter_by`, `sort_by` | `filter_by_status`, `sort_by_date` | 过滤/排序参数 |
| 请求体 | `payload`, `request_data` | `user_payload`, `order_data` | API请求数据 |
| 响应数据 | `response_data`, `result` | `api_response`, `search_results` | API响应数据 |
| API客户端 | `client`, `service` | `github_client`, `payment_service` | API服务调用对象 |
| 端点函数 | 资源+动作 | `get_user()`, `create_order()` | API处理函数 |
| 认证令牌 | `token`, `access_token` | `auth_token`, `api_key` | 认证凭据 |
| 错误响应 | `error`, `error_message` | `api_error`, `validation_errors` | API错误信息 |
| 分页数据 | `pagination`, `page_info` | `page_data`, `pagination_details` | 分页元信息 |

### 文件处理命名规范
| 操作类型 | 推荐命名 | 示例 | 说明 |
|---|---|---|---|
| 文件读取 | `read_` | `read_csv()`, `read_config_file()` | 读取文件内容 |
| 文件写入 | `write_` | `write_log()`, `write_report()` | 写入文件内容 |
| 数据加载 | `load_` | `load_dataset()`, `load_config()` | 加载结构化数据 |
| 数据保存 | `save_` | `save_results()`, `save_report()` | 保存数据到文件 |
| 文件上传 | `upload_` | `upload_to_s3()`, `upload_to_server()` | 上传文件操作 |
| 文件下载 | `download_` | `download_file()`, `download_backup()` | 下载文件操作 |
| 文件解析 | `parse_` | `parse_csv()`, `parse_xml()` | 解析文件内容 |
| 数据导出 | `export_` | `export_to_csv()`, `export_to_excel()` | 导出数据到文件 |
| 文件处理 | `process_` | `process_image()`, `process_upload()` | 处理文件内容 |
| 路径操作 | `path`, `dir_path` | `file_path`, `temp_dir` | 文件路径操作 |

### 测试代码命名规范
| 测试类型 | 推荐命名 | 示例 | 说明 |
|---|---|---|---|
| 单元测试 | `test_` | `test_create_user()`, `test_invalid_login()` | 单元测试函数 |
| 测试类 | `Test`+被测类名 | `TestUserService`, `TestOrderAPI` | 测试类命名 |
| Fixture | 描述性 | `db_session`, `api_client` | 测试夹具命名 |
| 测试数据集 | `test_data_` | `test_data_users`, `test_data_products` | 测试数据集 |
| 模拟对象 | `mock_` | `mock_request`, `mock_database` | 模拟对象变量 |
| 断言检查 | `assert_` | `assert_response()`, `assert_record_count()` | 自定义断言方法 |
| 测试参数 | `param_` | `param_user_data`, `param_invalid_values` | 参数化测试数据 |
| 性能测试 | `perf_test_` | `perf_test_large_dataset()` | 性能测试函数 |
| 集成测试 | `itest_` | `itest_order_workflow()` | 集成测试函数 |

### 常见错误与正确命名对比
| 错误命名 | 正确命名 | 说明 |
|---|---|---|
| `usrCnt` | `user_count` | 避免模糊缩写 |
| `data1` | `processed_data` | 避免模糊数字后缀 |
| `calc()` | `calculate_tax()` | 描述性函数名 |
| `process()` | `process_image_upload()` | 避免泛化动词 |
| `getData()` | `fetch_product_data()` | 遵循命名规范格式 |
| `retrieve()` | `get_user_by_email()` | 明确操作对象 |
| `val` | `is_valid` | 避免无意义缩写 |
| `tmp` | `temp_file` | 明确临时对象 |
| `doWork()` | `execute_background_task()` | 描述具体工作 |
| `saveResult` | `save_report_results()` | 明确保存内容 |

## 常见反模式和改进

### 代码结构反模式
```python
# ❌ 避免过长的函数
def process_everything():
    # 50行代码...
    pass

# ✅ 拆分为小函数
def validate_input(data):
    pass

def process_data(data):
    pass

def save_result(result):
    pass
```
