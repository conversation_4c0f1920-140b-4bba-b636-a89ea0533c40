#!/usr/bin/env python3
"""
示例任务 - 演示任务文件的基本结构
"""

# 任务名称: 示例数据处理任务
# 描述: 这是一个示例任务，用于演示任务系统的基本功能
# 作者: 系统管理员
# 版本: 1.0.0
# 分类: 示例

import time
import random
from datetime import datetime

# 导入全局配置
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared.global_config import G


def main():
    """任务主函数"""
    print(f"🚀 开始执行示例任务 - {datetime.now()}")

    # 使用全局配置
    print(f"📋 当前用户: {G.custom.user_name or '未设置'}")
    print(f"🌍 系统语言: {G.system.language}")
    print(f"🗂️ 上传目录: {G.files.upload_dir}")

    # 读取数据库配置
    print(f"🗄️ 数据库配置:")
    print(f"  MySQL主机: {G.database.mysql.host}")
    print(f"  SQLite路径: {G.database.sqlite.path}")

    # 设置一些运行时配置
    G.custom.last_run_task = "示例任务"
    G.custom.last_run_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # 创建任务统计配置
    current_count = G.get("task_stats.sample_task.run_count", 0)
    G.task_stats.sample_task.run_count = current_count + 1
    G.task_stats.sample_task.last_status = "running"

    print(f"📊 任务统计:")
    print(f"  运行次数: {G.task_stats.sample_task.run_count}")

    # 模拟任务执行
    items_to_process = G.get("task_config.sample_task.items", 5)
    for i in range(items_to_process):
        print(f"📊 处理数据 {i+1}/{items_to_process}...")
        time.sleep(random.uniform(0.5, 1.5))

    # 更新任务状态
    G.task_stats.sample_task.last_status = "success"
    G.task_stats.sample_task.last_run_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    print("✅ 示例任务执行完成")
    return {"status": "success", "processed_items": items_to_process}


if __name__ == "__main__":
    result = main()
    print(f"任务结果: {result}")
