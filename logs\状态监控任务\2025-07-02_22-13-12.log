# 任务执行日志
任务名称: 状态监控任务
开始时间: 2025-07-02 22:13:12
日志文件: 2025-07-02_22-13-12.log
==================================================

[2025-07-02 22:13:12] [INFO] 开始执行任务: 状态监控任务
[2025-07-02 22:13:12] [INFO] 调用任务run()方法
[2025-07-02 22:13:12] [SUCCESS] 任务执行完成，返回值: {'status': 'success', 'duration': 0.109801, 'run_count': 7, 'system_info': {'cpu_percent': 0.0, 'memory_percent': 51.7, 'memory_available_gb': 15.38, 'disk_percent': 44.7, 'disk_free_gb': 391.22}, 'task_stats': {'quick_task': {'run_count': 6, 'last_run': '2025-07-02 22:13:12', 'status': 'success'}, 'medium_task': {'run_count': 5, 'last_run': '2025-07-02 22:13:12', 'status': 'success'}, 'long_task': {'run_count': 5, 'last_run': '2025-07-02 22:13:12', 'status': 'success'}}, 'total_executions': 16, 'active_tasks': 3, 'message': '监控完成，发现 3 个活跃任务，总执行 16 次'}

==================================================
结束时间: 2025-07-02 22:13:12
执行状态: ✅ 成功
==================================================
