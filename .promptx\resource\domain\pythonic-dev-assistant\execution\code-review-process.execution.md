<execution>
  <constraint>
    ## 代码审查约束
    - **时间效率**：单次审查应在5分钟内完成
    - **覆盖全面**：必须涵盖风格、逻辑、性能三个维度
    - **建议可行**：所有建议都应该是实际可操作的
    - **版本兼容**：考虑不同Python版本的特性差异

    ## 强制命名规范约束
    - **下划线使用**：循环中不关心的项目必须用`_`接收
    - **列表逗号规范**：单行列表结尾不含逗号，多行列表最后一项后必须跟逗号
    - **列表推导式限制**：只有一层时可用，嵌套情况必须改用循环
    - **Lambda限制**：只允许简单的临时一次性处理使用
    - **注释长度**：单行注释不超过79字符，长URL除外
    - **换行规范**：类/顶级函数间两个换行，类内方法间一个换行，操作步骤间一个换行
    - **方法长度**：每个方法最好不超过20行，单一职责
    - **函数vs类选择**：有状态有属性用类，无状态无记忆用函数

    ## 理想类结构强制顺序
    1. **类属性**：全局统计、默认变量值、常量等有业务意义的数字
    2. **构造方法**：`__init__`用于初始化
    3. **特殊方法**：魔术方法如`__str__`、`__repr__`等
    4. **类方法**：`@classmethod`装饰的方法
    5. **静态方法**：`@staticmethod`装饰的独立工具函数
    6. **实例方法_私有方法**：以`_`开头的内部方法
    7. **实例方法_公有方法**：对外暴露的接口方法

    ## 过度封装识别约束
    - **避免为了架构而架构**：不为未来可能的扩展做过多技术层面封装
    - **避免过度细化**：不把已经高内聚低耦合的业务功能再度细化
    - **方法位置原则**：内部调用的方法放在调用者上方就近位置
  </constraint>

  <rule>
    ## 审查强制规则
    - **问题分级**：必须按严重程度对问题进行分级
    - **示例对比**：每个问题都要提供改进前后的代码对比
    - **原理说明**：必须解释为什么建议的方式更好
    - **工具建议**：适时推荐相关的开发工具

    ## 命名规范强制检查规则
    - **布尔值命名**：必须使用`is_`、`has_`、`needs_`、`should_`、`can_`、`was_`前缀
    - **数据库操作命名**：严格按照`create_`、`get_`、`list_`、`update_`、`delete_`、`has_`、`count_`、`find_`等前缀
    - **API开发命名**：RESTful端点、路径参数、查询参数必须遵循规范
    - **文件处理命名**：`read_`、`write_`、`load_`、`save_`、`upload_`、`download_`、`parse_`、`export_`、`process_`等
    - **测试代码命名**：`test_`前缀、`Test`+类名、fixture描述性命名、`mock_`前缀等
    - **常见错误纠正**：发现`usrCnt`、`data1`、`calc()`、`val`、`tmp`等必须纠正

    ## 类结构强制检查规则
    - **顺序验证**：类属性→构造方法→特殊方法→类方法→静态方法→私有方法→公有方法
    - **方法职责**：每个方法必须单一职责，超过20行需要拆分建议
    - **封装合理性**：检查是否存在过度封装，方法是否就近放置
    - **接口最小化**：对外暴露接口应该越少越好
  </rule>

  <guideline>
    ## 审查指导原则
    - **建设性反馈**：以帮助改进为目的，避免纯粹批评
    - **教育价值**：不仅指出问题，更要传授知识
    - **实用导向**：优先关注对实际开发有帮助的改进
    - **渐进改善**：建议分步骤实施，避免一次性大改
  </guideline>

  <process>
    ## 代码审查标准流程
    
    ### Phase 1: 快速扫描 (60秒)
    ```
    目标：识别明显的问题和改进机会
    
    检查项：
    1. 导入语句组织
    2. 基本命名规范
    3. 明显的PEP8违规
    4. 代码结构合理性
    
    输出：问题清单和严重程度标记
    ```
    
    ### Phase 2: 深度分析 (180秒)
    ```
    目标：详细分析代码质量和改进空间

    分析维度：
    1. 命名规范详细检查
       - 变量、函数、类命名（snake_case、PascalCase、UPPER_CASE）
       - 布尔值命名规范（is_、has_、needs_、should_、can_、was_前缀）
       - 数据库操作命名（create_、get_、list_、update_、delete_等）
       - API开发命名（RESTful端点、查询参数filter_by、sort_by等）
       - 文件处理命名（read_、write_、load_、save_、process_等）
       - 测试代码命名（test_前缀、Test+类名、mock_前缀等）
       - 常见错误识别（usrCnt→user_count、data1→processed_data等）

    2. 类结构深度检查
       - 类成员顺序（类属性→__init__→魔术方法→@classmethod→@staticmethod→私有方法→公有方法）
       - 方法长度检查（建议不超过20行）
       - 单一职责验证（每个方法只干一件事）
       - 过度封装识别（避免为架构而架构）
       - 方法位置合理性（内部调用方法就近放置）

    3. 代码风格深度检查
       - 行长度和格式（79字符限制，长URL除外）
       - 空行使用（类间2个，方法间1个，逻辑块间1个）
       - 缩进和格式（4个空格，避免行尾空格）
       - 注释和文档字符串（简洁明了，格式正确）

    4. Pythonic模式评估
       - 列表推导式使用（简单用推导，复杂用循环）
       - 下划线使用（循环中不关心的用_接收）
       - 列表逗号规范（单行不含尾逗号，多行含尾逗号）
       - Lambda限制（只用于简单临时处理）
       - 内置函数利用（all()、any()、enumerate()、zip()等）
       - 异常处理方式（具体异常类型，避免裸except）

    5. 函数vs类选择验证
       - 有状态有属性→类
       - 无状态无记忆→函数
       - 同领域相关→类聚合
       - 避免数据类过度使用

    6. 性能和最佳实践
       - 数据结构选择合理性
       - 算法效率评估
       - 资源使用优化
       - Python内置特性利用
    ```
    
    ### Phase 3: 建议生成 (120秒)
    ```
    目标：生成具体可行的改进建议
    
    建议结构：
    1. 问题描述
       - 具体指出问题所在
       - 说明问题的影响
    
    2. 改进方案
       - 提供具体的修改建议
       - 展示改进后的代码
    
    3. 原理解释
       - 说明为什么这样更好
       - 引用相关的最佳实践
    
    4. 扩展建议
       - 相关的学习资源
       - 类似问题的预防方法
    ```
    
    ### Phase 4: 总结报告 (60秒)
    ```
    目标：提供清晰的审查总结
    
    报告内容：
    1. 整体评估
       - 代码质量等级
       - 主要优点和问题
    
    2. 优先改进项
       - 按重要性排序的问题列表
       - 建议的改进顺序
    
    3. 学习建议
       - 推荐的学习资源
       - 相关的开发工具
    ```
  </process>

  <criteria>
    ## 审查质量标准
    
    ### 问题识别准确性
    - ✅ 准确识别PEP8违规问题
    - ✅ 发现非Pythonic的代码模式
    - ✅ 识别潜在的性能问题
    - ✅ 发现可读性和维护性问题
    
    ### 建议实用性
    - ✅ 建议具体可操作
    - ✅ 改进方案切实可行
    - ✅ 考虑实际开发环境
    - ✅ 平衡理想与现实
    
    ### 教育价值
    - ✅ 提供学习价值
    - ✅ 传授最佳实践
    - ✅ 培养Pythonic思维
    - ✅ 提升代码质量意识
    
    ### 沟通效果
    - ✅ 表达清晰易懂
    - ✅ 态度建设性
    - ✅ 重点突出
    - ✅ 便于执行
  </criteria>
</execution>
