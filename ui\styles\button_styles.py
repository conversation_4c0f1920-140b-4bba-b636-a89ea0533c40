"""
按钮样式常量模块
提供统一的按钮样式定义，支持主题化
"""

from typing import Dict


class ButtonStyles:
    """现代化按钮样式常量类"""
    
    # 基础样式模板
    BASE_STYLE = """
        QPushButton {{
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
            font-size: 14px;
            min-height: 16px;
        }}
        QPushButton:disabled {{
            opacity: 0.6;
            cursor: not-allowed;
        }}
    """
    
    # 主要按钮样式
    PRIMARY = BASE_STYLE + """
        QPushButton {
            background-color: #3b82f6;
            color: white;
        }
        QPushButton:hover {
            background-color: #2563eb;
        }
        QPushButton:pressed {
            background-color: #1d4ed8;
        }
    """
    
    # 成功按钮样式
    SUCCESS = BASE_STYLE + """
        QPushButton {
            background-color: #10b981;
            color: white;
        }
        QPushButton:hover {
            background-color: #059669;
        }
        QPushButton:pressed {
            background-color: #047857;
        }
    """
    
    # 危险按钮样式
    DANGER = BASE_STYLE + """
        QPushButton {
            background-color: #ef4444;
            color: white;
        }
        QPushButton:hover {
            background-color: #dc2626;
        }
        QPushButton:pressed {
            background-color: #b91c1c;
        }
    """
    
    # 次要按钮样式
    SECONDARY = BASE_STYLE + """
        QPushButton {
            background-color: #6b7280;
            color: white;
        }
        QPushButton:hover {
            background-color: #4b5563;
        }
        QPushButton:pressed {
            background-color: #374151;
        }
    """
    
    # 轮廓按钮样式
    OUTLINE = BASE_STYLE + """
        QPushButton {
            background-color: transparent;
            color: #3b82f6;
            border: 2px solid #3b82f6;
        }
        QPushButton:hover {
            background-color: #3b82f6;
            color: white;
        }
        QPushButton:pressed {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
        }
    """
    
    # 文本按钮样式
    TEXT = BASE_STYLE + """
        QPushButton {
            background-color: transparent;
            color: #3b82f6;
            padding: 8px 16px;
        }
        QPushButton:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }
        QPushButton:pressed {
            background-color: rgba(59, 130, 246, 0.2);
        }
    """

    @classmethod
    def get_style(cls, style_type: str) -> str:
        """获取指定类型的按钮样式
        
        Args:
            style_type: 样式类型（primary, success, danger, secondary, outline, text）
            
        Returns:
            CSS样式字符串
        """
        return getattr(cls, style_type.upper(), cls.PRIMARY)

    @classmethod
    def get_all_styles(cls) -> Dict[str, str]:
        """获取所有可用的按钮样式
        
        Returns:
            样式名称到CSS字符串的映射
        """
        return {
            'primary': cls.PRIMARY,
            'success': cls.SUCCESS,
            'danger': cls.DANGER,
            'secondary': cls.SECONDARY,
            'outline': cls.OUTLINE,
            'text': cls.TEXT,
        } 