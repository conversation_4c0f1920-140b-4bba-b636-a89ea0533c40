# 用户数据目录

此目录包含用户自定义的数据和配置。

## 目录结构

```
userdata/
├── database/          # 数据库配置
│   ├── mysql.json     # MySQL配置
│   ├── sqlite.json    # SQLite配置
│   └── redis.json     # Redis配置
├── api/               # API配置
│   ├── endpoints.json # API端点配置
│   └── tokens.json    # API令牌配置
├── files/             # 文件存储
│   ├── uploads/       # 上传文件
│   └── downloads/     # 下载文件
└── cache/             # 缓存目录
    ├── temp/          # 临时文件
    └── data/          # 数据缓存
```

## 使用说明

- 任务可以通过 `get_userdata_file()` 方法访问这些配置
- 所有用户数据都应该存储在此目录下
- 敏感信息请注意加密存储
