#!/usr/bin/env python3
"""
全局配置演示任务 - 展示如何使用G全局配置
"""

# 任务名称: 全局配置演示
# 描述: 演示如何使用G.xx的方式访问和设置全局配置
# 作者: 系统
# 版本: 1.0.0
# 分类: 演示

import sys
import os
from datetime import datetime

# 导入全局配置
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared.global_config import G


def run():
    """任务执行函数"""
    print("🎯 全局配置演示任务开始执行")
    
    # 1. 读取基本配置
    print("\n📋 读取基本配置:")
    print(f"  用户名: {G.custom.user_name or '未设置'}")
    print(f"  邮箱: {G.custom.email or '未设置'}")
    print(f"  系统语言: {G.system.language}")
    print(f"  调试模式: {G.system.debug}")
    
    # 2. 读取数据库配置
    print("\n🗄️ 数据库配置:")
    print(f"  MySQL主机: {G.database.mysql.host}")
    print(f"  MySQL端口: {G.database.mysql.port}")
    print(f"  SQLite路径: {G.database.sqlite.path}")
    print(f"  Redis主机: {G.database.redis.host}:{G.database.redis.port}")
    
    # 3. 读取API配置
    print("\n🌐 API配置:")
    print(f"  天气API: {G.api.weather.url}")
    print(f"  新闻API: {G.api.news.url}")
    print(f"  API超时: {G.api.weather.timeout}秒")
    
    # 4. 读取系统配置
    print("\n⚙️ 系统配置:")
    print(f"  调试模式: {G.system.debug}")
    print(f"  日志级别: {G.system.log_level}")
    print(f"  时区: {G.system.timezone}")
    print(f"  语言: {G.system.language}")
    
    # 5. 设置配置值
    print("\n✏️ 设置配置值:")
    G.custom.user_name = "演示用户"
    G.custom.email = "<EMAIL>"
    G.custom.last_login = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"  已设置用户名: {G.custom.user_name}")
    print(f"  已设置邮箱: {G.custom.email}")
    print(f"  已设置最后登录: {G.custom.last_login}")
    
    # 6. 创建新的配置节
    print("\n🆕 创建新配置节:")
    G.demo_task.name = "全局配置演示"
    G.demo_task.version = "1.0.0"
    G.demo_task.settings.auto_save = True
    G.demo_task.settings.max_retries = 3
    G.demo_task.settings.timeout = 30
    print(f"  任务名称: {G.demo_task.name}")
    print(f"  任务版本: {G.demo_task.version}")
    print(f"  自动保存: {G.demo_task.settings.auto_save}")
    print(f"  最大重试: {G.demo_task.settings.max_retries}")
    
    # 7. 使用get/set方法
    print("\n🔧 使用get/set方法:")
    # 设置深层嵌套配置
    G.set("app.features.notifications.email", True)
    G.set("app.features.notifications.sms", False)
    G.set("app.features.backup.enabled", True)
    G.set("app.features.backup.interval", 24)
    
    # 读取配置（带默认值）
    email_enabled = G.get("app.features.notifications.email", False)
    sms_enabled = G.get("app.features.notifications.sms", False)
    backup_interval = G.get("app.features.backup.interval", 12)
    unknown_config = G.get("app.unknown.config", "默认值")
    
    print(f"  邮件通知: {email_enabled}")
    print(f"  短信通知: {sms_enabled}")
    print(f"  备份间隔: {backup_interval}小时")
    print(f"  未知配置: {unknown_config}")
    
    # 8. 任务统计
    print("\n📊 任务统计:")
    current_count = G.get("task_stats.global_config_demo.run_count", 0)
    G.task_stats.global_config_demo.run_count = current_count + 1
    G.task_stats.global_config_demo.last_run = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    G.task_stats.global_config_demo.status = "success"
    
    print(f"  运行次数: {G.task_stats.global_config_demo.run_count}")
    print(f"  最后运行: {G.task_stats.global_config_demo.last_run}")
    print(f"  运行状态: {G.task_stats.global_config_demo.status}")
    
    # 9. 动态配置
    print("\n🔄 动态配置:")
    # 根据时间设置不同的配置
    hour = datetime.now().hour
    if 6 <= hour < 18:
        G.app.mode = "day"
        G.app.theme = "light"
    else:
        G.app.mode = "night"
        G.app.theme = "dark"
    
    print(f"  当前模式: {G.app.mode}")
    print(f"  当前主题: {G.app.theme}")
    
    # 10. 配置验证
    print("\n✅ 配置验证:")
    required_configs = [
        "database.mysql.host",
        "api.weather.url",
        "system.language"
    ]
    
    for config_path in required_configs:
        value = G.get(config_path)
        if value:
            print(f"  ✓ {config_path}: {value}")
        else:
            print(f"  ✗ {config_path}: 未配置")
    
    print("\n✅ 全局配置演示任务执行完成")
    
    return {
        "status": "success",
        "user_name": G.custom.user_name,
        "run_count": G.task_stats.global_config_demo.run_count,
        "app_mode": G.app.mode
    }


if __name__ == "__main__":
    result = run()
    print(f"\n📋 任务结果: {result}")
