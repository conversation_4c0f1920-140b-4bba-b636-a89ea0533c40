"""
任务调度器 - 核心调度逻辑
"""

import threading
import time
import importlib.util
import sys
import io
import contextlib
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, Future

from .task_scanner import TaskScanner, TaskInfo
from .config_manager import ConfigManager
from .scheduler_config import SchedulerConfigManager
from .frequency_parser import FrequencyParser
from logs.task_logger import TaskLogger


class LogCapture:
    """日志捕获器 - 将print输出重定向到日志文件"""

    def __init__(self, log_file_path: str, task_logger: TaskLogger):
        self.log_file_path = log_file_path
        self.task_logger = task_logger
        self.captured_output = io.StringIO()
        self.original_stdout = sys.stdout

    def __enter__(self):
        # 重定向stdout到我们的捕获器
        sys.stdout = self.captured_output
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # 恢复原始stdout
        sys.stdout = self.original_stdout

        # 获取捕获的输出
        captured_text = self.captured_output.getvalue()

        # 将捕获的输出写入日志文件
        if captured_text.strip():
            lines = captured_text.strip().split('\n')
            for line in lines:
                if line.strip():
                    self.task_logger.write_log(self.log_file_path, "TASK_OUTPUT", line.strip())

        # 清理
        self.captured_output.close()


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, 
                 task_scanner: TaskScanner,
                 config_manager: ConfigManager,
                 task_logger: TaskLogger,
                 status_callback: Optional[Callable] = None):
        """
        初始化调度器
        
        Args:
            task_scanner: 任务扫描器
            config_manager: 任务配置管理器
            task_logger: 任务日志管理器
            status_callback: 状态更新回调函数
        """
        self.task_scanner = task_scanner
        self.config_manager = config_manager
        self.task_logger = task_logger
        self.scheduler_config = SchedulerConfigManager()
        self.frequency_parser = FrequencyParser()
        self.status_callback = status_callback
        
        # 调度器状态
        self.is_running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self.executor: Optional[ThreadPoolExecutor] = None
        self.running_tasks: Dict[str, Future] = {}  # 正在运行的任务
        
        # 统计信息
        self.last_check_time: Optional[datetime] = None
        self.next_check_time: Optional[datetime] = None
        
        print("✅ 任务调度器初始化完成")
    
    def start(self) -> bool:
        """启动调度器"""
        if self.is_running:
            print("⚠️ 调度器已经在运行中")
            return False
        
        try:
            # 更新配置状态
            self.scheduler_config.set_scheduler_enabled(True)
            
            # 创建线程池
            max_workers = self.scheduler_config.get_config().max_concurrent
            self.executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="TaskExecutor")
            
            # 启动调度线程
            self.is_running = True
            self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            self.scheduler_thread.start()
            
            print(f"🚀 任务调度器已启动 (最大并发: {max_workers})")
            self._notify_status_change("调度器已启动")
            
            return True
            
        except Exception as e:
            print(f"❌ 启动调度器失败: {e}")
            self.is_running = False
            return False
    
    def stop(self) -> bool:
        """停止调度器"""
        if not self.is_running:
            print("⚠️ 调度器未在运行")
            return False
        
        try:
            # 停止调度循环
            self.is_running = False
            
            # 等待正在运行的任务完成
            if self.running_tasks:
                print(f"⏳ 等待 {len(self.running_tasks)} 个任务完成...")
                for task_name, future in self.running_tasks.items():
                    try:
                        future.result(timeout=5)  # 等待5秒
                    except:
                        print(f"⚠️ 任务 {task_name} 未能及时完成")
            
            # 关闭线程池
            if self.executor:
                self.executor.shutdown(wait=True)
                self.executor = None
            
            # 等待调度线程结束
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=5)
            
            # 更新配置状态
            self.scheduler_config.set_scheduler_enabled(False)
            
            print("⏹️ 任务调度器已停止")
            self._notify_status_change("调度器已停止")
            
            return True
            
        except Exception as e:
            print(f"❌ 停止调度器失败: {e}")
            return False
    
    def _scheduler_loop(self) -> None:
        """调度器主循环"""
        print("🔄 调度器循环开始")
        
        while self.is_running:
            try:
                current_time = datetime.now()
                self.last_check_time = current_time
                
                # 更新最后检查时间
                self.scheduler_config.update_last_check_time(
                    current_time.strftime("%Y-%m-%d %H:%M:%S")
                )
                
                # 检查需要执行的任务
                self._check_and_execute_tasks(current_time)
                
                # 清理已完成的任务
                self._cleanup_completed_tasks()
                
                # 计算下次检查时间
                check_interval = self.scheduler_config.get_config().check_interval
                self.next_check_time = current_time + timedelta(seconds=check_interval)
                
                # 通知状态更新
                self._notify_status_change(f"检查完成，下次检查: {self.next_check_time.strftime('%H:%M:%S')}")
                
                # 等待下次检查
                time.sleep(check_interval)
                
            except Exception as e:
                print(f"❌ 调度器循环错误: {e}")
                time.sleep(10)  # 出错时等待10秒再继续
        
        print("🔄 调度器循环结束")
    
    def _check_and_execute_tasks(self, current_time: datetime) -> None:
        """检查并执行到期的任务"""
        try:
            # 获取所有任务
            tasks = self.task_scanner.scan_tasks()
            
            for task in tasks:
                # 获取任务配置
                config = self.config_manager.get_task_config(task.name)
                
                # 跳过禁用的任务
                if not config.is_enabled:
                    continue
                
                # 跳过正在运行的任务
                if task.name in self.running_tasks:
                    continue
                
                # 检查是否到了执行时间
                if config.next_run_time and self.frequency_parser.is_time_to_run(config.next_run_time, current_time):
                    self._execute_task_async(task)
                    
        except Exception as e:
            print(f"❌ 检查任务时出错: {e}")
    
    def _execute_task_async(self, task: TaskInfo) -> None:
        """异步执行任务"""
        try:
            print(f"🚀 开始执行任务: {task.name}")
            
            # 提交任务到线程池
            future = self.executor.submit(self._execute_task, task)
            self.running_tasks[task.name] = future
            
            # 更新任务状态为执行中
            self.config_manager.set_task_status(task.name, "执行中")
            self._notify_status_change(f"开始执行任务: {task.name}")
            print(f"🔄 任务状态更新: {task.name} → 执行中")
            
        except Exception as e:
            print(f"❌ 提交任务执行失败: {task.name} - {e}")
    
    def _execute_task(self, task: TaskInfo) -> None:
        """执行单个任务"""
        log_file = None
        
        try:
            # 创建任务日志文件
            log_file = self.task_logger.create_log_file(task.name)
            self.task_logger.write_log(log_file, "INFO", f"开始执行任务: {task.name}")
            
            # 动态导入任务模块
            spec = importlib.util.spec_from_file_location(task.name, task.file_path)
            if spec is None or spec.loader is None:
                raise ImportError(f"无法加载任务模块: {task.file_path}")
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 使用日志捕获器执行任务函数
            with LogCapture(log_file, self.task_logger):
                # 查找并执行任务函数（优先run，其次main）
                if hasattr(module, 'run'):
                    self.task_logger.write_log(log_file, "INFO", "调用任务run()方法")
                    result = module.run()
                elif hasattr(module, 'main'):
                    self.task_logger.write_log(log_file, "INFO", "调用任务main()方法")
                    result = module.main()
                else:
                    raise AttributeError(f"任务文件中未找到run()或main()函数: {task.file_path}")

            self.task_logger.write_log(log_file, "SUCCESS", f"任务执行完成，返回值: {result}")

            # 完成日志记录
            self.task_logger.finish_log(log_file, success=True)

            # 更新统计
            self.scheduler_config.increment_executed()

            print(f"✅ 任务执行成功: {task.name}")
            self._notify_status_change(f"任务执行成功: {task.name}")

        except Exception as e:
            error_msg = f"任务执行失败: {str(e)}"
            print(f"❌ {task.name} - {error_msg}")
            
            # 记录错误日志
            if log_file:
                self.task_logger.write_log(log_file, "ERROR", error_msg)
                self.task_logger.finish_log(log_file, success=False, error_msg=str(e))
            
            # 更新失败统计
            self.scheduler_config.increment_failed()
            
        finally:
            # 更新任务配置
            self._update_task_after_execution(task.name)
            
            # 从运行列表中移除
            if task.name in self.running_tasks:
                del self.running_tasks[task.name]
    
    def _update_task_after_execution(self, task_name: str) -> None:
        """任务执行后更新配置"""
        try:
            # 增加运行次数
            self.config_manager.increment_run_count(task_name)
            
            # 计算下次运行时间
            config = self.config_manager.get_task_config(task_name)
            if config.frequency_minutes > 0:
                next_run_time = self.frequency_parser.calculate_next_run_time(
                    config.frequency_minutes, 
                    config.last_run_time
                )
                self.config_manager.update_task_config(task_name, next_run_time=next_run_time)
            
            # 恢复状态为等待中
            self.config_manager.set_task_status(task_name, "等待中")
            print(f"🔄 任务状态更新: {task_name} → 等待中")
            self._notify_status_change(f"任务完成，状态恢复: {task_name} → 等待中")
            
        except Exception as e:
            print(f"❌ 更新任务配置失败: {task_name} - {e}")
    
    def _cleanup_completed_tasks(self) -> None:
        """清理已完成的任务"""
        completed_tasks = []
        
        for task_name, future in self.running_tasks.items():
            if future.done():
                completed_tasks.append(task_name)
        
        for task_name in completed_tasks:
            del self.running_tasks[task_name]
    
    def _notify_status_change(self, message: str) -> None:
        """通知状态变化"""
        if self.status_callback:
            try:
                self.status_callback(message)
            except Exception as e:
                print(f"❌ 状态回调失败: {e}")
    
    def get_status(self) -> Dict:
        """获取调度器状态"""
        config = self.scheduler_config.get_config()
        
        return {
            'is_running': self.is_running,
            'last_check_time': self.last_check_time.strftime("%Y-%m-%d %H:%M:%S") if self.last_check_time else "",
            'next_check_time': self.next_check_time.strftime("%Y-%m-%d %H:%M:%S") if self.next_check_time else "",
            'running_tasks_count': len(self.running_tasks),
            'running_tasks': list(self.running_tasks.keys()),
            'total_executed': config.total_executed,
            'total_failed': config.total_failed,
            'success_rate': self.scheduler_config.get_success_rate(),
            'check_interval': config.check_interval,
            'max_concurrent': config.max_concurrent,
        }
    
    def execute_task_immediately(self, task_name: str) -> bool:
        """立即执行指定任务"""
        try:
            # 查找任务
            tasks = self.task_scanner.scan_tasks()
            target_task = None
            
            for task in tasks:
                if task.name == task_name:
                    target_task = task
                    break
            
            if not target_task:
                print(f"❌ 未找到任务: {task_name}")
                return False
            
            # 检查是否已在运行
            if task_name in self.running_tasks:
                print(f"⚠️ 任务已在运行中: {task_name}")
                return False
            
            # 执行任务
            if self.executor:
                self._execute_task_async(target_task)
                return True
            else:
                print("❌ 调度器未启动，无法执行任务")
                return False
                
        except Exception as e:
            print(f"❌ 立即执行任务失败: {task_name} - {e}")
            return False
