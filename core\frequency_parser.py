"""
频率解析器 - 解析各种格式的执行频率
"""

import re
from datetime import datetime, timedelta
from typing import Optional, Union
try:
    from croniter import croniter
except ImportError:
    print("❌ 缺少croniter库，请安装: pip install croniter")
    croniter = None


class FrequencyParser:
    """频率解析器"""
    
    @staticmethod
    def parse_frequency(frequency_str: Union[str, int]) -> Optional[int]:
        """
        解析频率字符串，返回分钟数
        
        支持格式:
        - 数字: 30 (30分钟)
        - 字符串数字: "30" (30分钟)
        - Cron表达式: "0 */2 * * *" (每2小时)
        - 自然语言: "every 1 hour", "daily"
        """
        if isinstance(frequency_str, int):
            return frequency_str if frequency_str > 0 else None
        
        if isinstance(frequency_str, str):
            frequency_str = frequency_str.strip()
            
            # 尝试解析为数字
            if frequency_str.isdigit():
                minutes = int(frequency_str)
                return minutes if minutes > 0 else None
            
            # 尝试解析Cron表达式
            if FrequencyParser.is_cron_expression(frequency_str):
                return FrequencyParser.cron_to_minutes(frequency_str)
            
            # 尝试解析自然语言
            return FrequencyParser.parse_natural_language(frequency_str)
        
        return None
    
    @staticmethod
    def is_cron_expression(expr: str) -> bool:
        """判断是否为Cron表达式"""
        try:
            # Cron表达式通常有5个字段，用空格分隔
            parts = expr.split()
            if len(parts) != 5:
                return False
            
            # 验证Cron表达式的有效性
            croniter(expr)
            return True
        except:
            return False
    
    @staticmethod
    def cron_to_minutes(cron_expr: str) -> Optional[int]:
        """
        将Cron表达式转换为大概的分钟间隔
        注意：这是一个近似值，用于显示目的
        """
        try:
            # 常见的Cron表达式模式
            patterns = {
                r'^\*/(\d+) \* \* \* \*$': lambda m: int(m.group(1)),  # */5 * * * * (每5分钟)
                r'^0 \*/(\d+) \* \* \*$': lambda m: int(m.group(1)) * 60,  # 0 */2 * * * (每2小时)
                r'^0 0 \* \* \*$': lambda m: 24 * 60,  # 0 0 * * * (每天)
                r'^0 0 \* \* 0$': lambda m: 7 * 24 * 60,  # 0 0 * * 0 (每周)
                r'^0 (\d+) \* \* \*$': lambda m: 24 * 60,  # 0 9 * * * (每天9点)
            }
            
            for pattern, converter in patterns.items():
                match = re.match(pattern, cron_expr)
                if match:
                    return converter(match)
            
            # 如果无法识别模式，返回默认值
            return 60  # 默认1小时
            
        except Exception as e:
            print(f"❌ Cron表达式解析失败: {e}")
            return None
    
    @staticmethod
    def parse_natural_language(text: str) -> Optional[int]:
        """解析自然语言频率描述"""
        text = text.lower().strip()
        
        # 预定义的自然语言模式
        patterns = {
            r'every (\d+) minutes?': lambda m: int(m.group(1)),
            r'every (\d+) hours?': lambda m: int(m.group(1)) * 60,
            r'every (\d+) days?': lambda m: int(m.group(1)) * 24 * 60,
            r'hourly': lambda m: 60,
            r'daily': lambda m: 24 * 60,
            r'weekly': lambda m: 7 * 24 * 60,
            r'monthly': lambda m: 30 * 24 * 60,
        }
        
        for pattern, converter in patterns.items():
            match = re.search(pattern, text)
            if match:
                return converter(match)
        
        return None
    
    @staticmethod
    def calculate_next_run_time(frequency_minutes: int, last_run_time: Optional[str] = None) -> str:
        """
        计算下次运行时间
        
        Args:
            frequency_minutes: 频率(分钟)
            last_run_time: 最后运行时间字符串
            
        Returns:
            下次运行时间字符串
        """
        try:
            if last_run_time:
                # 从最后运行时间计算
                last_time = datetime.strptime(last_run_time, "%Y-%m-%d %H:%M:%S")
                next_time = last_time + timedelta(minutes=frequency_minutes)
            else:
                # 从当前时间计算
                next_time = datetime.now() + timedelta(minutes=frequency_minutes)
            
            return next_time.strftime("%Y-%m-%d %H:%M:%S")
            
        except Exception as e:
            print(f"❌ 计算下次运行时间失败: {e}")
            # 返回当前时间加频率
            next_time = datetime.now() + timedelta(minutes=frequency_minutes)
            return next_time.strftime("%Y-%m-%d %H:%M:%S")
    
    @staticmethod
    def calculate_next_run_time_cron(cron_expr: str, base_time: Optional[datetime] = None) -> str:
        """
        使用Cron表达式计算下次运行时间
        
        Args:
            cron_expr: Cron表达式
            base_time: 基准时间，默认为当前时间
            
        Returns:
            下次运行时间字符串
        """
        try:
            if base_time is None:
                base_time = datetime.now()
            
            cron = croniter(cron_expr, base_time)
            next_time = cron.get_next(datetime)
            
            return next_time.strftime("%Y-%m-%d %H:%M:%S")
            
        except Exception as e:
            print(f"❌ Cron表达式计算下次运行时间失败: {e}")
            # 回退到1小时后
            next_time = datetime.now() + timedelta(hours=1)
            return next_time.strftime("%Y-%m-%d %H:%M:%S")
    
    @staticmethod
    def is_time_to_run(next_run_time: str, current_time: Optional[datetime] = None) -> bool:
        """
        检查是否到了运行时间
        
        Args:
            next_run_time: 下次运行时间字符串
            current_time: 当前时间，默认为系统当前时间
            
        Returns:
            是否到了运行时间
        """
        try:
            if current_time is None:
                current_time = datetime.now()
            
            target_time = datetime.strptime(next_run_time, "%Y-%m-%d %H:%M:%S")
            
            # 允许1分钟的误差
            return current_time >= target_time
            
        except Exception as e:
            print(f"❌ 检查运行时间失败: {e}")
            return False


if __name__ == "__main__":
    # 测试频率解析器
    parser = FrequencyParser()
    
    print("🧪 测试频率解析器:")
    
    # 测试各种格式
    test_cases = [
        30,                    # 数字
        "60",                  # 字符串数字
        "0 */2 * * *",         # Cron表达式 - 每2小时
        "*/15 * * * *",        # Cron表达式 - 每15分钟
        "0 0 * * *",           # Cron表达式 - 每天
        "every 30 minutes",    # 自然语言
        "hourly",              # 自然语言
        "daily",               # 自然语言
    ]
    
    for case in test_cases:
        result = parser.parse_frequency(case)
        print(f"  '{case}' → {result} 分钟")
    
    # 测试时间计算
    print("\n⏰ 测试时间计算:")
    next_time = parser.calculate_next_run_time(30)
    print(f"  30分钟后: {next_time}")
    
    # 测试Cron时间计算
    cron_next = parser.calculate_next_run_time_cron("0 */2 * * *")
    print(f"  Cron(每2小时): {cron_next}")
    
    # 测试运行时间检查
    print(f"\n✅ 是否到运行时间: {parser.is_time_to_run(next_time)}")
