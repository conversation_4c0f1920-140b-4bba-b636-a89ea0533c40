{"fix_stats": {"ui_sync": {"run_count": 3, "last_run": "2025-07-03 00:03:51", "issues_found": 4, "fixes_applied": 4, "status": "completed", "duration": 0.005002}, "expired_time": {"run_count": 2, "last_run": "2025-07-03 00:03:51", "test_cases_processed": 3, "expired_cases_fixed": 3, "status": "completed", "duration": 0.005509}}, "ui_stats": {"realtime_refresh": {"run_count": 2, "last_run": "2025-07-03 00:03:51", "mechanisms_implemented": 4, "components_optimized": 5, "performance_tests": {"refresh_latency": "< 100ms", "ui_responsiveness": "95%", "memory_usage": "稳定", "cpu_impact": "< 2%"}, "status": "completed", "duration": 2.608817}}, "test_stats": {"long_task": {"run_count": 2, "last_run": "2025-07-03 00:03:54", "last_duration": 8.899921, "last_processed_count": 20, "total_processed": 40, "avg_duration": 8.8577165, "status": "success"}, "medium_task": {"run_count": 2, "last_run": "2025-07-03 00:04:03", "last_duration": 5.65734, "last_processed_count": 4, "total_processed": 11, "status": "success"}, "monitor_task": {"run_count": 2, "last_run": "2025-07-03 00:04:10", "last_system_info": {"cpu_percent": 1.9, "memory_percent": 52.7, "memory_available_gb": 15.05, "disk_percent": 44.9, "disk_free_gb": 389.68}, "last_task_stats": {"quick_task": {"run_count": 0, "last_run": "从未执行", "status": "未知"}, "medium_task": {"run_count": 2, "last_run": "2025-07-03 00:04:03", "status": "success"}, "long_task": {"run_count": 2, "last_run": "2025-07-03 00:03:54", "status": "success"}}, "total_executions_monitored": 4, "status": "success", "last_duration": 0.106677}}, "optimize_stats": {"scheduler": {"run_count": 2, "last_run": "2025-07-03 00:04:09", "optimizations_applied": 6, "performance_baseline": {"cpu_usage": 4.0, "memory_usage": 52.7, "memory_available": 15.05}, "test_results": {"config_save_reduction": "减少70%的配置保存操作", "status_check_optimization": "状态检查效率提升50%", "thread_pool_improvement": "线程池利用率提升30%", "log_write_optimization": "日志写入速度提升40%"}, "status": "completed", "duration": 1.711074}}, "check_stats": {"stability": {"run_count": 2, "last_run": "2025-07-03 00:04:10", "total_checks": 16, "passed_checks": 10, "pass_rate": 62.5, "stability_grade": "需改进", "check_results": {"配置管理": [{"item": "配置文件完整性", "status": "failed"}, {"item": "配置加载机制", "status": "passed"}, {"item": "配置保存机制", "status": "failed"}, {"item": "配置备份策略", "status": "passed"}], "任务调度": [{"item": "调度器启动/停止", "status": "failed"}, {"item": "任务发现机制", "status": "passed"}, {"item": "任务执行状态", "status": "passed"}, {"item": "并发任务处理", "status": "passed"}], "错误处理": [{"item": "异常捕获机制", "status": "passed"}, {"item": "错误日志记录", "status": "failed"}, {"item": "故障恢复能力", "status": "passed"}, {"item": "资源清理机制", "status": "failed"}], "性能监控": [{"item": "内存使用情况", "status": "passed"}, {"item": "CPU占用率", "status": "failed"}, {"item": "线程池状态", "status": "passed"}, {"item": "文件句柄管理", "status": "passed"}]}, "status": "completed", "duration": 3.208858}}, "validation_stats": {"completeness": {"run_count": 1, "last_run": "2025-07-03 00:03:47", "total_features": 20, "implemented_features": 16, "completion_rate": 80.0, "completeness_grade": "部分完整", "validation_results": {"任务管理": [{"feature": "任务自动发现", "status": "implemented"}, {"feature": "任务配置管理", "status": "implemented"}, {"feature": "任务状态跟踪", "status": "implemented"}, {"feature": "任务执行统计", "status": "implemented"}], "调度系统": [{"feature": "定时调度执行", "status": "implemented"}, {"feature": "并发任务处理", "status": "implemented"}, {"feature": "频率解析支持", "status": "implemented"}, {"feature": "过期时间处理", "status": "implemented"}], "配置系统": [{"feature": "全局配置管理", "status": "implemented"}, {"feature": "任务配置持久化", "status": "implemented"}, {"feature": "调度器配置", "status": "pending"}, {"feature": "用户数据管理", "status": "pending"}], "日志系统": [{"feature": "任务执行日志", "status": "implemented"}, {"feature": "错误日志记录", "status": "implemented"}, {"feature": "日志文件管理", "status": "pending"}, {"feature": "日志查看功能", "status": "implemented"}], "用户界面": [{"feature": "任务列表显示", "status": "pending"}, {"feature": "状态实时更新", "status": "implemented"}, {"feature": "操作按钮功能", "status": "implemented"}, {"feature": "右键菜单操作", "status": "implemented"}]}, "status": "completed", "duration": 4.014929}}}