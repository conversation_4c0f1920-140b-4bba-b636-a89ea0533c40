# Python标准规范知识体系

## PEP8核心规范

### 命名约定
```python
# 变量和函数：snake_case
user_name = "张三"
def calculate_total_price():
    pass

# 类名：PascalCase  
class UserManager:
    pass

# 常量：UPPER_CASE
MAX_RETRY_COUNT = 3

# 私有成员：前缀下划线
class MyClass:
    def __init__(self):
        self._private_var = "私有变量"
        self.__very_private = "强私有变量"
```

### 布尔值命名规范
```python
# 状态检查
is_active = True
is_valid = False

# 资源存在
has_permission = True
has_file = False

# 操作需求
needs_update = True
needs_backup = False

# 行为建议
should_retry = True
should_warn = False

# 可执行状态
can_edit = True
can_delete = False

# 已完成状态
was_processed = True
was_validated = False
```

### 代码格式规范
```python
# 行长度：不超过79字符
# 缩进：4个空格
# 空行：顶级函数和类之间2个空行，类内方法之间1个空行

class ExampleClass:
    """示例类"""
    
    def __init__(self):
        self.value = 0
    
    def method_one(self):
        """第一个方法"""
        pass
    
    def method_two(self):
        """第二个方法"""
        pass


def top_level_function():
    """顶级函数"""
    pass


def another_function():
    """另一个顶级函数"""
    pass
```

## Pythonic编程模式

### 列表推导式规范
```python
# ✅ 好的做法 - 简单推导式
squares = [x**2 for x in range(10)]
even_numbers = [x for x in range(20) if x % 2 == 0]

# ❌ 避免 - 复杂嵌套推导式
# 改用循环更清晰
result = []
for outer in data:
    for inner in outer:
        if condition(inner):
            result.append(process(inner))
```

### 内置函数优先使用
```python
# ✅ 使用all()检查所有元素
if all(item.is_valid for item in items):
    process_items()

# ✅ 使用any()检查至少一个元素
if any(item.needs_update for item in items):
    update_items()

# ✅ 使用enumerate()而非range(len())
for i, item in enumerate(items):
    print(f"{i}: {item}")

# ✅ 使用zip()处理多个序列
for name, age in zip(names, ages):
    print(f"{name} is {age} years old")
```

### 下划线的使用
```python
# ✅ 忽略不需要的值
for _ in range(10):
    print("Hello")

# ✅ 解包时忽略不需要的值
first, _, last = name_parts

# ✅ 忽略循环变量
for _, value in dictionary.items():
    process(value)
```

### 列表逗号使用规范
```python
# ✅ 单行列表 - 不要尾随逗号
colors = ['red', 'green', 'blue']

# ✅ 多行列表 - 推荐尾随逗号
long_list = [
    'item1',
    'item2',
    'item3',
]

# ✅ 多行字典 - 推荐尾随逗号
config = {
    'host': 'localhost',
    'port': 8080,
    'debug': True,
}
```

## 文档字符串规范

### 标准格式
```python
def read_lines_for_file(file_path: str) -> list:
    """read_lines_for_file 从文件读取行列表，并且过滤空行和换行符
    
    如果还有其他描述继续这里写
    如果有api文档写在这里
    
    Note:
        需要注意的关键点
        
    Args:
        file_path: 文件路径参数说明
        
    Returns:
        返回过滤后的行列表
        
    Raises:
        FileNotFoundError: 文件不存在时抛出
        PermissionError: 权限不足时抛出
    """
    pass
```

## 类设计最佳实践

### 理想的类结构
```python
class YXFCollection:
    # 类属性(全局统计，默认变量值，常量等有业务意义的数字)
    collection_count = 0
    max_number_of_retries = 3
    
    # 构造方法，用于初始化
    def __init__(self, username: str, password: str) -> None:
        # 实例属性 每个实例独有的，不需要多实例之间共享
        self.username = username
        self.password = password
    
    # 特殊的方法（魔术方法）
    def __str__(self) -> str:
        """__str__ 友好显示（最有用的一个）
        让 print(bot) 显示有意义的信息，调试时很方便
        """
        return f'执行的用户名：{self.username}'
    
    # 类方法(类属性读写，不实例)
    @classmethod
    def get_execute_data(cls) -> dict:
        """get_execute_data 获取类属性执行数据"""
        return {
            "collection_count": cls.collection_count,
            "max_number_of_retries": cls.max_number_of_retries,
        }
    
    # 静态方法（独立的纯工具函数）不依赖于实例
    @staticmethod
    def validate_url(url: str) -> bool:
        """静态方法：验证URL格式"""
        return url.startswith(('http://', 'https://')) and len(url) > 10
    
    # 实例方法_私有方法
    def _handle_product_data(self, product_data: dict) -> dict:
        """将采集的原始数据处理为规整的字典"""
        return product_data
    
    # 实例方法_公有方法
    def collection_product(self, url: str) -> dict:
        """采集商品数据"""
        return {}
```

### 函数 vs 类的选择原则
- **使用类**：有状态、有属性、需要方法的"有血有肉"情况
- **使用函数**：无状态、无记忆、用完即抛弃的工具函数
- **同领域相关功能**：可考虑用类聚合

## 常见反模式和改进

### 命名反模式
```python
# ❌ 错误命名
usrCnt = 10  # 模糊缩写
data1 = []   # 无意义数字后缀
val = True   # 无意义缩写

# ✅ 正确命名
user_count = 10
processed_data = []
is_valid = True
```

### 代码结构反模式
```python
# ❌ 避免过长的函数
def process_everything():
    # 50行代码...
    pass

# ✅ 拆分为小函数
def validate_input(data):
    pass

def process_data(data):
    pass

def save_result(result):
    pass
```
