#!/usr/bin/env python3
"""
调试任务配置更新问题
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from core.config_manager import ConfigManager
from datetime import datetime

def test_config_update():
    """测试配置更新功能"""
    print("🔍 开始调试任务配置更新...")
    
    # 创建配置管理器
    config_manager = ConfigManager()
    
    # 显示当前配置
    print("\n📋 当前任务配置:")
    all_configs = config_manager.get_all_configs()
    for name, config in all_configs.items():
        if name in ["快速测试任务", "中等延迟任务", "长时间任务", "状态监控任务"]:
            print(f"  {name}:")
            print(f"    状态: {config.status}")
            print(f"    运行次数: {config.run_count}")
            print(f"    最后运行: {config.last_run_time}")
            print(f"    下次运行: {config.next_run_time}")
    
    # 测试更新功能
    print("\n🧪 测试配置更新功能:")
    
    test_task = "快速测试任务"
    print(f"\n测试任务: {test_task}")
    
    # 获取当前配置
    config_before = config_manager.get_task_config(test_task)
    print(f"更新前 - 运行次数: {config_before.run_count}, 最后运行: {config_before.last_run_time}")
    
    # 测试增加运行次数
    try:
        print("执行 increment_run_count...")
        config_manager.increment_run_count(test_task)
        print("✅ increment_run_count 执行成功")
    except Exception as e:
        print(f"❌ increment_run_count 失败: {e}")
        return
    
    # 获取更新后的配置
    config_after = config_manager.get_task_config(test_task)
    print(f"更新后 - 运行次数: {config_after.run_count}, 最后运行: {config_after.last_run_time}")
    
    # 测试状态更新
    try:
        print("执行 set_task_status...")
        config_manager.set_task_status(test_task, "执行中")
        print("✅ set_task_status 执行成功")
        
        config_status = config_manager.get_task_config(test_task)
        print(f"状态更新后: {config_status.status}")
        
        # 恢复状态
        config_manager.set_task_status(test_task, "等待中")
        print("✅ 状态已恢复为等待中")
        
    except Exception as e:
        print(f"❌ set_task_status 失败: {e}")
    
    # 显示最终配置
    print("\n📋 最终任务配置:")
    final_configs = config_manager.get_all_configs()
    for name, config in final_configs.items():
        if name in ["快速测试任务", "中等延迟任务", "长时间任务", "状态监控任务"]:
            print(f"  {name}:")
            print(f"    状态: {config.status}")
            print(f"    运行次数: {config.run_count}")
            print(f"    最后运行: {config.last_run_time}")
    
    print("\n✅ 调试完成")


if __name__ == "__main__":
    test_config_update()
