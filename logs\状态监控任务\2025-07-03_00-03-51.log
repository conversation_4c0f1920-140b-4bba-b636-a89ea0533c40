🚀 状态监控任务 - 2025-07-03 00:03:51
==================================================
[2025-07-03 00:04:10] [TASK_OUTPUT] 📊 状态监控任务开始执行 - 00:04:10
[2025-07-03 00:04:10] [TASK_OUTPUT] ❌ 状态回调失败: wrapped C/C++ object of type LogWidget has been deleted
[2025-07-03 00:04:10] [TASK_OUTPUT] 🔍 第 2 次监控检查
[2025-07-03 00:04:10] [TASK_OUTPUT] ❌ 状态回调失败: wrapped C/C++ object of type LogWidget has been deleted
[2025-07-03 00:04:10] [TASK_OUTPUT] 💻 系统状态:
[2025-07-03 00:04:10] [TASK_OUTPUT] CPU使用率: 1.9%
[2025-07-03 00:04:10] [TASK_OUTPUT] 内存使用率: 52.7% (可用: 15.05GB)
[2025-07-03 00:04:10] [TASK_OUTPUT] 磁盘使用率: 44.9% (可用: 389.68GB)
[2025-07-03 00:04:10] [TASK_OUTPUT] 📈 任务执行统计:
[2025-07-03 00:04:10] [TASK_OUTPUT] quick_task: 执行0次, 状态:未知, 最后执行:从未执行
[2025-07-03 00:04:10] [TASK_OUTPUT] medium_task: 执行2次, 状态:success, 最后执行:2025-07-03 00:04:03
[2025-07-03 00:04:10] [TASK_OUTPUT] long_task: 执行2次, 状态:success, 最后执行:2025-07-03 00:03:54
[2025-07-03 00:04:10] [TASK_OUTPUT] 📊 总体统计:
[2025-07-03 00:04:10] [TASK_OUTPUT] 活跃任务数: 2/3
[2025-07-03 00:04:10] [TASK_OUTPUT] 总执行次数: 4
[2025-07-03 00:04:10] [TASK_OUTPUT] 监控运行次数: 2
[2025-07-03 00:04:10] [TASK_OUTPUT] ✅ 状态监控完成 - 耗时: 0.1秒
==================================================
✅ 完成 - 00:04:10
