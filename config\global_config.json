{"database": {"mysql": {"host": "*************", "port": 3306, "username": "testuser", "password": "", "database": "", "charset": "utf8mb4"}, "sqlite": {"path": "app.db", "timeout": 30}, "redis": {"host": "localhost", "port": 6379, "password": "", "db": 0}}, "api": {"weather": {"url": "https://api.openweathermap.org/data/2.5/weather", "key": "your-api-key-here", "timeout": 30}, "news": {"url": "https://newsapi.org/v2/top-headlines", "key": "", "timeout": 30}}, "system": {"debug": false, "log_level": "INFO", "timezone": "Asia/Shanghai", "language": "zh-CN"}, "custom": {"user_name": "演示用户", "email": "<EMAIL>", "phone": "", "app_name": "任务调度系统", "version": "1.0.0", "last_login": "2025-07-02 21:54:29"}, "test": {"deep": {"nested": {"value": "深层配置"}}}, "demo_task": {"name": "全局配置演示", "version": "1.0.0", "settings": {"auto_save": true, "max_retries": 3, "timeout": 30}}, "app": {"features": {"notifications": {"email": true, "sms": false}, "backup": {"enabled": true, "interval": 24}}, "mode": "night", "theme": "dark"}, "task_stats": {"global_config_demo": {"run_count": 1, "last_run": "2025-07-02 21:54:29", "status": "success"}}, "test_stats": {"long_task": {"run_count": 20, "last_run": "2025-07-02 23:25:56", "last_duration": 9.637918, "last_processed_count": 14, "total_processed": 199, "avg_duration": 9.26246342202381, "status": "success"}, "medium_task": {"run_count": 20, "last_run": "2025-07-02 23:25:56", "last_duration": 2.610664, "last_processed_count": 3, "total_processed": 64, "status": "success"}, "quick_task": {"run_count": 6, "last_run": "2025-07-02 22:13:12", "last_duration": 1.507365, "status": "success"}, "monitor_task": {"run_count": 21, "last_run": "2025-07-02 23:25:57", "last_system_info": {"cpu_percent": 1.0, "memory_percent": 56.6, "memory_available_gb": 13.81, "disk_percent": 44.7, "disk_free_gb": 391.07}, "last_task_stats": {"quick_task": {"run_count": 6, "last_run": "2025-07-02 22:13:12", "status": "success"}, "medium_task": {"run_count": 20, "last_run": "2025-07-02 23:25:56", "status": "success"}, "long_task": {"run_count": 20, "last_run": "2025-07-02 23:25:56", "status": "success"}}, "total_executions_monitored": 46, "status": "success", "last_duration": 0.106624}}, "fix_stats": {"ui_sync": {"run_count": 14, "last_run": "2025-07-02 23:25:56", "issues_found": 4, "fixes_applied": 4, "status": "completed", "duration": 0.011982}, "expired_time": {"run_count": 14, "last_run": "2025-07-02 23:25:56", "test_cases_processed": 3, "expired_cases_fixed": 3, "status": "completed", "duration": 0.008517}}, "ui_stats": {"realtime_refresh": {"run_count": 13, "last_run": "2025-07-02 23:25:56", "mechanisms_implemented": 4, "components_optimized": 5, "performance_tests": {"refresh_latency": "< 100ms", "ui_responsiveness": "95%", "memory_usage": "稳定", "cpu_impact": "< 2%"}, "status": "completed", "duration": 2.611679}}, "validation_stats": {"completeness": {"run_count": 9, "last_run": "2025-07-02 23:25:58", "total_features": 20, "implemented_features": 14, "completion_rate": 70.0, "completeness_grade": "需要完善", "validation_results": {"任务管理": [{"feature": "任务自动发现", "status": "implemented"}, {"feature": "任务配置管理", "status": "implemented"}, {"feature": "任务状态跟踪", "status": "pending"}, {"feature": "任务执行统计", "status": "implemented"}], "调度系统": [{"feature": "定时调度执行", "status": "pending"}, {"feature": "并发任务处理", "status": "implemented"}, {"feature": "频率解析支持", "status": "implemented"}, {"feature": "过期时间处理", "status": "implemented"}], "配置系统": [{"feature": "全局配置管理", "status": "implemented"}, {"feature": "任务配置持久化", "status": "implemented"}, {"feature": "调度器配置", "status": "pending"}, {"feature": "用户数据管理", "status": "pending"}], "日志系统": [{"feature": "任务执行日志", "status": "implemented"}, {"feature": "错误日志记录", "status": "pending"}, {"feature": "日志文件管理", "status": "implemented"}, {"feature": "日志查看功能", "status": "implemented"}], "用户界面": [{"feature": "任务列表显示", "status": "pending"}, {"feature": "状态实时更新", "status": "implemented"}, {"feature": "操作按钮功能", "status": "implemented"}, {"feature": "右键菜单操作", "status": "implemented"}]}, "status": "completed", "duration": 4.020017}}, "optimize_stats": {"scheduler": {"run_count": 12, "last_run": "2025-07-02 23:25:56", "optimizations_applied": 6, "performance_baseline": {"cpu_usage": 0.0, "memory_usage": 56.6, "memory_available": 13.82}, "test_results": {"config_save_reduction": "减少70%的配置保存操作", "status_check_optimization": "状态检查效率提升50%", "thread_pool_improvement": "线程池利用率提升30%", "log_write_optimization": "日志写入速度提升40%"}, "status": "completed", "duration": 1.71103}}, "check_stats": {"stability": {"run_count": 10, "last_run": "2025-07-02 23:25:57", "total_checks": 16, "passed_checks": 13, "pass_rate": 81.2, "stability_grade": "良好", "check_results": {"配置管理": [{"item": "配置文件完整性", "status": "failed"}, {"item": "配置加载机制", "status": "passed"}, {"item": "配置保存机制", "status": "passed"}, {"item": "配置备份策略", "status": "passed"}], "任务调度": [{"item": "调度器启动/停止", "status": "passed"}, {"item": "任务发现机制", "status": "passed"}, {"item": "任务执行状态", "status": "passed"}, {"item": "并发任务处理", "status": "passed"}], "错误处理": [{"item": "异常捕获机制", "status": "failed"}, {"item": "错误日志记录", "status": "passed"}, {"item": "故障恢复能力", "status": "failed"}, {"item": "资源清理机制", "status": "passed"}], "性能监控": [{"item": "内存使用情况", "status": "passed"}, {"item": "CPU占用率", "status": "passed"}, {"item": "线程池状态", "status": "passed"}, {"item": "文件句柄管理", "status": "passed"}]}, "status": "completed", "duration": 3.209376}}}