# 任务执行日志
任务名称: 状态监控任务
开始时间: 2025-07-02 22:35:20
日志文件: 2025-07-02_22-35-20.log
==================================================

[2025-07-02 22:35:20] [INFO] 开始执行任务: 状态监控任务
[2025-07-02 22:35:20] [INFO] 调用任务run()方法
[2025-07-02 22:35:21] [SUCCESS] 任务执行完成，返回值: {'status': 'success', 'duration': 0.1115, 'run_count': 11, 'system_info': {'cpu_percent': 2.0, 'memory_percent': 52.8, 'memory_available_gb': 15.0, 'disk_percent': 44.7, 'disk_free_gb': 391.21}, 'task_stats': {'quick_task': {'run_count': 6, 'last_run': '2025-07-02 22:13:12', 'status': 'success'}, 'medium_task': {'run_count': 9, 'last_run': '2025-07-02 22:35:19', 'status': 'success'}, 'long_task': {'run_count': 9, 'last_run': '2025-07-02 22:35:19', 'status': 'success'}}, 'total_executions': 24, 'active_tasks': 3, 'message': '监控完成，发现 3 个活跃任务，总执行 24 次'}

==================================================
结束时间: 2025-07-02 22:35:21
执行状态: ✅ 成功
==================================================
