"""
调度器配置管理 - 处理调度器的配置持久化
"""

import json
from pathlib import Path
from dataclasses import dataclass, asdict
from typing import Dict, Any


@dataclass
class SchedulerConfig:
    """调度器配置数据类"""
    check_interval: int = 30          # 检查间隔(秒)
    max_concurrent: int = 10          # 最大并发任务数
    auto_start: bool = True           # 启动时自动开始调度
    retry_failed: bool = True         # 失败任务是否准备下次执行
    log_level: str = "INFO"           # 日志级别
    scheduler_enabled: bool = False   # 调度器是否启用
    last_check_time: str = ""         # 最后检查时间
    total_executed: int = 0           # 总执行次数
    total_failed: int = 0             # 总失败次数


class SchedulerConfigManager:
    """调度器配置管理器"""
    
    def __init__(self, config_file: str = "scheduler_config.json"):
        self.config_file = Path(config_file)
        self.config = SchedulerConfig()
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        if not self.config_file.exists():
            self.save_config()
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 更新配置对象
            for key, value in data.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
            
            print(f"✅ 加载调度器配置成功")
            
        except Exception as e:
            print(f"❌ 加载调度器配置失败: {e}")
            self.config = SchedulerConfig()
    
    def save_config(self) -> None:
        """保存配置文件"""
        try:
            data = asdict(self.config)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 保存调度器配置成功")
            
        except Exception as e:
            print(f"❌ 保存调度器配置失败: {e}")
    
    def update_config(self, **kwargs) -> None:
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        
        self.save_config()
    
    def get_config(self) -> SchedulerConfig:
        """获取配置对象"""
        return self.config
    
    def set_scheduler_enabled(self, enabled: bool) -> None:
        """设置调度器启用状态"""
        self.update_config(scheduler_enabled=enabled)
    
    def increment_executed(self) -> None:
        """增加执行次数"""
        self.config.total_executed += 1
        self.save_config()
    
    def increment_failed(self) -> None:
        """增加失败次数"""
        self.config.total_failed += 1
        self.save_config()
    
    def update_last_check_time(self, check_time: str) -> None:
        """更新最后检查时间"""
        self.update_config(last_check_time=check_time)
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.config.total_executed == 0:
            return 0.0
        
        success_count = self.config.total_executed - self.config.total_failed
        return (success_count / self.config.total_executed) * 100
    
    def reset_statistics(self) -> None:
        """重置统计数据"""
        self.update_config(
            total_executed=0,
            total_failed=0,
            last_check_time=""
        )


if __name__ == "__main__":
    # 测试配置管理器
    manager = SchedulerConfigManager()
    
    print("📋 当前调度器配置:")
    config = manager.get_config()
    print(f"  检查间隔: {config.check_interval}秒")
    print(f"  最大并发: {config.max_concurrent}个")
    print(f"  自动启动: {config.auto_start}")
    print(f"  调度器状态: {'启用' if config.scheduler_enabled else '禁用'}")
    print(f"  总执行次数: {config.total_executed}")
    print(f"  总失败次数: {config.total_failed}")
    print(f"  成功率: {manager.get_success_rate():.1f}%")
    
    # 测试更新配置
    manager.update_config(check_interval=60, max_concurrent=5)
    print("\n✅ 配置更新测试完成")
