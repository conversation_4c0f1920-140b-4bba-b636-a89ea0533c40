#!/usr/bin/env python3
"""
快速测试任务 - 1分钟间隔执行，用于测试调度器
"""

# 任务名称: 快速测试任务
# 描述: 每1分钟执行一次，用于快速测试调度器功能
# 作者: 测试
# 版本: 1.0.0
# 分类: 测试

import sys
import os
import time
from datetime import datetime

# 导入全局配置
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared.global_config import G


def run():
    """任务执行函数"""
    start_time = datetime.now()
    print(f"🚀 快速测试任务开始执行 - {start_time.strftime('%H:%M:%S')}")
    
    # 记录任务统计
    current_count = G.get("test_stats.quick_task.run_count", 0)
    G.test_stats.quick_task.run_count = current_count + 1
    G.test_stats.quick_task.last_run = start_time.strftime("%Y-%m-%d %H:%M:%S")
    
    print(f"📊 这是第 {G.test_stats.quick_task.run_count} 次执行")
    
    # 模拟一些工作（短时间）
    print("⚡ 执行快速处理...")
    for i in range(3):
        print(f"  📝 处理步骤 {i+1}/3")
        time.sleep(0.5)  # 0.5秒延迟
    
    # 记录完成时间
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    G.test_stats.quick_task.last_duration = duration
    G.test_stats.quick_task.status = "success"
    
    print(f"✅ 快速测试任务完成 - 耗时: {duration:.1f}秒")
    
    return {
        "status": "success",
        "duration": duration,
        "run_count": G.test_stats.quick_task.run_count,
        "message": "快速测试任务执行成功"
    }


if __name__ == "__main__":
    result = run()
    print(f"📋 任务结果: {result}")
