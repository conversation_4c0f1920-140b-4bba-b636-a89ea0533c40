"""
全局配置管理器 - 提供G.xx的全局配置访问方式
"""

import json
from pathlib import Path
from typing import Any, Dict


class GlobalConfig:
    """全局配置类，支持G.xx的访问方式"""
    
    def __init__(self, config_file: str = "config/global_config.json"):
        self.config_file = Path(config_file)
        self._config_data: Dict[str, Any] = {}
        # 确保config目录存在
        self.config_file.parent.mkdir(exist_ok=True)
        self._load_config()
    

    
    def _load_config(self) -> None:
        """加载配置文件"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config_data = json.load(f)
                print(f"✅ 加载全局配置: {len(self._config_data)} 项")
            except Exception as e:
                print(f"❌ 加载全局配置失败: {e}")
                self._config_data = {}
        else:
            # 创建默认配置
            self._create_default_config()
    
    def _create_default_config(self) -> None:
        """创建默认配置"""
        default_config = {
            "database": {
                "mysql": {
                    "host": "localhost",
                    "port": 3306,
                    "username": "",
                    "password": "",
                    "database": "",
                    "charset": "utf8mb4"
                },
                "sqlite": {
                    "path": "app.db",
                    "timeout": 30
                },
                "redis": {
                    "host": "localhost",
                    "port": 6379,
                    "password": "",
                    "db": 0
                }
            },
            "api": {
                "weather": {
                    "url": "https://api.openweathermap.org/data/2.5/weather",
                    "key": "",
                    "timeout": 30
                },
                "news": {
                    "url": "https://newsapi.org/v2/top-headlines",
                    "key": "",
                    "timeout": 30
                }
            },
            "system": {
                "debug": False,
                "log_level": "INFO",
                "timezone": "Asia/Shanghai",
                "language": "zh-CN"
            },
            "custom": {
                "user_name": "",
                "email": "",
                "phone": ""
            }
        }
        
        self._config_data = default_config
        self._save_config()
        print("📄 创建默认全局配置文件")
    
    def _save_config(self) -> None:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config_data, f, ensure_ascii=False, indent=2)
            print(f"💾 保存全局配置成功")
        except Exception as e:
            print(f"❌ 保存全局配置失败: {e}")
    
    def __getattr__(self, name: str) -> Any:
        """支持G.xx的访问方式"""
        if name.startswith('_'):
            # 私有属性直接返回
            return super().__getattribute__(name)
        
        if name in self._config_data:
            value = self._config_data[name]
            if isinstance(value, dict):
                # 如果是字典，返回一个支持链式访问的对象
                return ConfigSection(value, self, name)
            return value
        else:
            # 如果不存在，创建一个新的配置节
            self._config_data[name] = {}
            self._save_config()
            return ConfigSection(self._config_data[name], self, name)
    
    def __setattr__(self, name: str, value: Any) -> None:
        """支持G.xx = value的设置方式"""
        if name.startswith('_') or name in ['config_file']:
            # 私有属性或特殊属性直接设置
            super().__setattr__(name, value)
        else:
            # 配置属性保存到配置数据中
            if not hasattr(self, '_config_data'):
                super().__setattr__(name, value)
                return
            
            self._config_data[name] = value
            self._save_config()
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的路径"""
        keys = key.split('.')
        value = self._config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值，支持点号分隔的路径"""
        keys = key.split('.')
        config = self._config_data
        
        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置最后一级的值
        config[keys[-1]] = value
        self._save_config()
    
    def reload(self) -> None:
        """重新加载配置文件"""
        self._load_config()
    
    def to_dict(self) -> Dict[str, Any]:
        """返回配置字典"""
        return self._config_data.copy()


class ConfigSection:
    """配置节类，支持链式访问"""
    
    def __init__(self, data: Dict[str, Any], parent: GlobalConfig, path: str):
        self._data = data
        self._parent = parent
        self._path = path
    
    def __getattr__(self, name: str) -> Any:
        """支持G.xx.yy的访问方式"""
        if name.startswith('_'):
            return super().__getattribute__(name)
        
        if name in self._data:
            value = self._data[name]
            if isinstance(value, dict):
                return ConfigSection(value, self._parent, f"{self._path}.{name}")
            return value
        else:
            # 如果不存在，创建一个新的配置节
            self._data[name] = {}
            self._parent._save_config()
            return ConfigSection(self._data[name], self._parent, f"{self._path}.{name}")
    
    def __setattr__(self, name: str, value: Any) -> None:
        """支持G.xx.yy = value的设置方式"""
        if name.startswith('_'):
            super().__setattr__(name, value)
        else:
            self._data[name] = value
            self._parent._save_config()
    
    def __str__(self) -> str:
        """字符串表示"""
        return str(self._data)
    
    def __repr__(self) -> str:
        """调试表示"""
        return f"ConfigSection({self._path}): {self._data}"


# 创建全局配置实例
G = GlobalConfig()


def reload_global_config():
    """重新加载全局配置"""
    global G
    G.reload()


def get_global_config() -> GlobalConfig:
    """获取全局配置实例"""
    return G


if __name__ == "__main__":
    # 测试全局配置
    print("🧪 测试全局配置系统:")
    
    # 测试基本访问
    print(f"\n📋 数据库配置:")
    print(f"  MySQL主机: {G.database.mysql.host}")
    print(f"  MySQL端口: {G.database.mysql.port}")
    
    # 测试设置
    G.database.mysql.host = "*************"
    G.database.mysql.username = "testuser"
    print(f"\n✏️ 设置后:")
    print(f"  MySQL主机: {G.database.mysql.host}")
    print(f"  MySQL用户: {G.database.mysql.username}")
    
    # 测试新配置
    G.custom.app_name = "任务调度系统"
    G.custom.version = "1.0.0"
    print(f"\n🆕 自定义配置:")
    print(f"  应用名称: {G.custom.app_name}")
    print(f"  版本: {G.custom.version}")
    
    # 测试深层嵌套
    G.test.deep.nested.value = "深层配置"
    print(f"\n🔗 深层嵌套:")
    print(f"  深层值: {G.test.deep.nested.value}")
    
    # 测试get/set方法
    G.set("api.weather.key", "your-api-key-here")
    api_key = G.get("api.weather.key", "default-key")
    print(f"\n🔑 API密钥: {api_key}")
    
    print(f"\n✅ 全局配置测试完成")
