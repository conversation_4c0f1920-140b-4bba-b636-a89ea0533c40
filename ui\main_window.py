"""
主窗口UI界面
现代化、美观、简约的自动化任务调度管理界面
"""

import sys
from pathlib import Path
from typing import List, Optional
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTableWidget, QTableWidgetItem, QPushButton,
    QLabel, QLineEdit, QComboBox, QTextEdit, QSplitter,
    QGroupBox, QGridLayout, QStatusBar,
    QHeaderView, QFrame, QMessageBox, QDialog, QMenu
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QIcon, QColor

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.task_scanner import TaskScanner, TaskInfo
from core.config_manager import ConfigManager
from core.task_scheduler import TaskScheduler
from core.directory_manager import DirectoryManager
from logs.task_logger import TaskLogger


class ModernButton(QPushButton):
    """现代化按钮样式"""
    
    def __init__(self, text: str, button_type: str = "primary"):
        super().__init__(text)
        self.set_button_style(button_type)
    
    def set_button_style(self, button_type: str) -> None:
        """设置按钮样式"""
        base_style = """
            QPushButton {
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: 600;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: rgba(0, 0, 0, 0.1);
            }
            QPushButton:pressed {
                background-color: rgba(0, 0, 0, 0.2);
            }
        """
        
        if button_type == "primary":
            style = base_style + """
                QPushButton {
                    background-color: #3b82f6;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #2563eb;
                }
                QPushButton:pressed {
                    background-color: #1d4ed8;
                }
            """
        elif button_type == "success":
            style = base_style + """
                QPushButton {
                    background-color: #10b981;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #059669;
                }
                QPushButton:pressed {
                    background-color: #047857;
                }
            """
        elif button_type == "danger":
            style = base_style + """
                QPushButton {
                    background-color: #ef4444;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #dc2626;
                }
                QPushButton:pressed {
                    background-color: #b91c1c;
                }
            """
        elif button_type == "secondary":
            style = base_style + """
                QPushButton {
                    background-color: #6b7280;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #4b5563;
                }
                QPushButton:pressed {
                    background-color: #374151;
                }
            """
        
        self.setStyleSheet(style)


class TaskTableWidget(QTableWidget):
    """任务列表表格"""

    def __init__(self):
        super().__init__()
        self.tasks_data: List[TaskInfo] = []
        self.setup_table()

    def setup_table(self) -> None:
        """设置表格"""
        # 设置列
        headers = ["任务名称", "状态", "频率(分钟)", "下次运行时间", "运行次数", "最后运行时间"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        # 显示行号作为序号
        self.setVerticalHeaderLabels([])  # 清空默认标签
        self.verticalHeader().setVisible(True)  # 确保行号可见
        
        # 设置表格样式
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #e5e7eb;
                background-color: white;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #f3f4f6;
            }
            QTableWidget::item:selected {
                background-color: #eff6ff;
                color: #1e40af;
            }
            QHeaderView::section {
                background-color: #f9fafb;
                padding: 12px;
                border: none;
                border-bottom: 2px solid #e5e7eb;
                font-weight: 600;
                color: #374151;
            }
        """)
        
        # 设置表格属性
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)  # 禁用编辑
        self.setSelectionMode(QTableWidget.SelectionMode.NoSelection)  # 禁用选中效果

        # 连接点击事件用于任务选择
        self.itemClicked.connect(self.on_item_clicked)

        # 设置列宽
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # 任务名称 - 自适应
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)    # 状态 - 固定宽度
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)    # 频率 - 固定宽度
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)    # 下次运行时间 - 固定宽度
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)    # 已运行次数 - 固定宽度
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)    # 最后运行时间 - 固定宽度

        # 设置具体列宽
        self.setColumnWidth(1, 100)  # 状态 - 增加宽度
        self.setColumnWidth(2, 80)   # 频率
        self.setColumnWidth(3, 150)  # 下次运行时间
        self.setColumnWidth(4, 70)   # 运行次数 - 缩短宽度
        self.setColumnWidth(5, 150)  # 最后运行时间

        # 设置行号样式
        self.verticalHeader().setDefaultSectionSize(35)  # 行高
        self.verticalHeader().setMinimumSectionSize(35)
        self.verticalHeader().setStyleSheet("""
            QHeaderView::section {
                background-color: #f9fafb;
                border: 1px solid #e5e7eb;
                padding: 8px;
                font-weight: 600;
                color: #374151;
                text-align: center;
            }
        """)

        # 启用右键菜单
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

    def load_tasks(self, tasks: List[TaskInfo], config_manager) -> None:
        """加载任务数据到表格"""
        self.tasks_data = tasks
        self.setRowCount(len(tasks))

        # 设置行号标签
        row_labels = [str(i + 1) for i in range(len(tasks))]
        self.setVerticalHeaderLabels(row_labels)

        for row, task in enumerate(tasks):
            # 获取任务配置
            config = config_manager.get_task_config(task.name)

            # 任务名称
            self.setItem(row, 0, QTableWidgetItem(task.name))

            # 状态
            status_text = self.get_status_display_text(config.status)
            status_item = QTableWidgetItem(status_text)
            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

            if config.status == "执行中":
                # 绿色背景 + 深绿色文字
                status_item.setBackground(QColor("#dcfce7"))
                status_item.setForeground(QColor("#166534"))
                status_item.setFont(self.get_bold_font())
            elif config.status == "已禁用":
                # 红色背景 + 深红色文字
                status_item.setBackground(QColor("#fef2f2"))
                status_item.setForeground(QColor("#991b1b"))
                status_item.setFont(self.get_bold_font())
            else:  # 等待中
                # 蓝色背景 + 深蓝色文字
                status_item.setBackground(QColor("#dbeafe"))
                status_item.setForeground(QColor("#1e40af"))
                status_item.setFont(self.get_bold_font())

            self.setItem(row, 1, status_item)

            # 频率(分钟)
            freq_item = QTableWidgetItem(str(config.frequency_minutes))
            freq_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 2, freq_item)

            # 下次运行时间
            next_run = config.next_run_time if config.next_run_time else "未设置"
            self.setItem(row, 3, QTableWidgetItem(next_run))

            # 已运行次数
            count_item = QTableWidgetItem(str(config.run_count))
            count_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.setItem(row, 4, count_item)

            # 最后运行时间
            last_run = config.last_run_time if config.last_run_time else "从未运行"
            self.setItem(row, 5, QTableWidgetItem(last_run))

    def get_selected_task(self) -> Optional[TaskInfo]:
        """获取当前选中的任务"""
        current_row = self.currentRow()
        if 0 <= current_row < len(self.tasks_data):
            return self.tasks_data[current_row]
        return None

    def get_bold_font(self):
        """获取粗体字体"""
        from PyQt6.QtGui import QFont
        font = QFont()
        font.setBold(True)
        font.setPointSize(9)
        return font

    def get_status_display_text(self, status: str) -> str:
        """获取状态显示文本（带图标）"""
        status_icons = {
            "执行中": "🟢 执行中",
            "等待中": "🔵 等待中",
            "已禁用": "🔴 已禁用"
        }
        return status_icons.get(status, f"⚪ {status}")

    def on_item_clicked(self, item):
        """处理表格项点击事件"""
        if item is None:
            return

        row = item.row()
        if 0 <= row < len(self.tasks_data):
            selected_task = self.tasks_data[row]
            # 触发任务选择事件
            main_window = self.window()
            if hasattr(main_window, 'on_task_selected_by_click'):
                main_window.on_task_selected_by_click(selected_task)

    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.itemAt(position)
        if item is None:
            return

        # 通过位置获取任务
        row = item.row()
        if row < 0 or row >= len(self.tasks_data):
            return

        selected_task = self.tasks_data[row]

        # 获取主窗口的config_manager
        main_window = self.window()
        if not hasattr(main_window, 'config_manager'):
            return

        config = main_window.config_manager.get_task_config(selected_task.name)

        menu = QMenu(self)

        # 设置菜单样式 - 适中大小的外观
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #d1d5db;
                border-radius: 8px;
                padding: 6px 0px;
                font-size: 13px;
                font-weight: 500;
                min-width: 160px;
                color: #374151;
            }
            QMenu::item {
                background-color: transparent;
                padding: 8px 16px;
                margin: 1px 6px;
                border-radius: 6px;
                color: #374151;
                font-weight: 500;
                border: none;
            }
            QMenu::item:selected {
                background-color: #f3f4f6;
                color: #1f2937;
            }
            QMenu::item:pressed {
                background-color: #e5e7eb;
                color: #1f2937;
            }
            QMenu::separator {
                height: 1px;
                background-color: #e5e7eb;
                margin: 4px 12px;
                border: none;
            }
        """)

        # 立即执行
        run_action = menu.addAction("▶️ 立即执行")
        run_action.triggered.connect(lambda: self.run_task(selected_task))

        menu.addSeparator()

        # 设置频率 - 简化选项
        freq_action = menu.addAction("⏰ 设置频率")
        freq_action.triggered.connect(lambda: self.set_frequency_minutes(selected_task))

        # 自定义语法
        custom_action = menu.addAction("🔧 自定义语法")
        custom_action.triggered.connect(lambda: self.set_custom_syntax(selected_task))

        # 查看日志
        log_action = menu.addAction("📋 查看日志")
        log_action.triggered.connect(lambda: self.view_task_logs(selected_task))

        menu.addSeparator()

        # 启用/禁用
        if config.is_enabled:
            disable_action = menu.addAction("⏸️ 禁用任务")
            disable_action.triggered.connect(lambda: self.toggle_task(selected_task, False))
        else:
            enable_action = menu.addAction("▶️ 启用任务")
            enable_action.triggered.connect(lambda: self.toggle_task(selected_task, True))

        menu.exec(self.mapToGlobal(position))

    def run_task(self, task: TaskInfo):
        """运行任务"""
        main_window = self.window()

        # 使用调度器立即执行任务
        if main_window.scheduler.execute_task_immediately(task.name):
            main_window.log_display.append(f"[{main_window.get_current_time()}] ▶️ 已提交任务执行: {task.name}")
        else:
            main_window.log_display.append(f"[{main_window.get_current_time()}] ❌ 任务执行失败: {task.name}")

        # 刷新界面
        main_window.load_tasks()

    def set_frequency_minutes(self, task: TaskInfo):
        """设置任务执行频率(分钟)"""
        from PyQt6.QtWidgets import QInputDialog

        main_window = self.window()
        current_freq = main_window.config_manager.get_task_config(task.name).frequency_minutes

        # 显示输入对话框
        minutes, ok = QInputDialog.getInt(
            self,
            "设置执行频率",
            f"请输入任务 '{task.name}' 的执行频率(分钟):",
            value=current_freq,
            min=1,
            max=525600,  # 最大一年
            step=1
        )

        if ok:
            main_window.config_manager.set_frequency(task.name, minutes)
            main_window.log_display.append(
                f"[{main_window.get_current_time()}] ⏰ 设置任务 '{task.name}' 频率为 {minutes} 分钟"
            )
            # 刷新表格显示
            main_window.load_tasks()

    def set_custom_syntax(self, task: TaskInfo):
        """设置自定义执行语法"""
        from PyQt6.QtWidgets import QInputDialog

        main_window = self.window()

        # 显示多行文本输入对话框
        current_syntax = "0 */1 * * *"  # 默认每小时执行
        syntax, ok = QInputDialog.getMultiLineText(
            self,
            "自定义执行语法",
            f"请输入任务 '{task.name}' 的自定义执行语法:\n\n"
            "支持格式:\n"
            "• Cron表达式: 0 */2 * * * (每2小时)\n"
            "• 时间间隔: every 30 minutes\n"
            "• 具体时间: at 09:00, 18:00",
            current_syntax
        )

        if ok and syntax.strip():
            main_window.log_display.append(
                f"[{main_window.get_current_time()}] 🔧 设置任务 '{task.name}' 自定义语法: {syntax.strip()}"
            )
            # 这里后续实现自定义语法解析和保存逻辑

    def toggle_task(self, task: TaskInfo, enabled: bool):
        """启用/禁用任务"""
        main_window = self.window()
        action = "启用" if enabled else "禁用"

        main_window.config_manager.enable_task(task.name, enabled)
        main_window.log_display.append(
            f"[{main_window.get_current_time()}] {'▶️' if enabled else '⏸️'} {action}任务: {task.name}"
        )

        # 刷新表格显示
        main_window.load_tasks()

    def view_task_logs(self, task: TaskInfo):
        """查看任务日志"""
        main_window = self.window()

        # 获取任务日志列表
        logs = main_window.task_logger.get_task_logs(task.name, limit=20)

        if not logs:
            main_window.log_display.append(
                f"[{main_window.get_current_time()}] ℹ️ 任务 '{task.name}' 暂无执行日志"
            )
            return

        # 创建日志查看对话框
        self.show_log_viewer_dialog(task.name, logs)

    def show_log_viewer_dialog(self, task_name: str, logs: list):
        """显示日志查看对话框"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QTextEdit, QPushButton, QLabel, QSplitter

        dialog = QDialog(self)
        dialog.setWindowTitle(f"任务日志 - {task_name}")
        dialog.setGeometry(200, 200, 800, 600)

        layout = QVBoxLayout(dialog)

        # 标题
        title_label = QLabel(f"📋 任务: {task_name}")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px;")
        layout.addWidget(title_label)

        # 分割器
        splitter = QSplitter()
        layout.addWidget(splitter)

        # 左侧：日志文件列表
        log_list = QListWidget()
        log_list.setMaximumWidth(250)
        for log in logs:
            log_list.addItem(f"{log['file_name']} ({log['created_time']})")
        splitter.addWidget(log_list)

        # 右侧：日志内容
        log_content = QTextEdit()
        log_content.setReadOnly(True)
        log_content.setStyleSheet("font-family: 'Consolas', 'Monaco', monospace; font-size: 12px;")
        splitter.addWidget(log_content)

        # 按钮
        button_layout = QHBoxLayout()
        refresh_btn = QPushButton("🔄 刷新")
        close_btn = QPushButton("❌ 关闭")

        button_layout.addStretch()
        button_layout.addWidget(refresh_btn)
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)

        # 事件处理
        def load_log_content():
            current_row = log_list.currentRow()
            if current_row >= 0 and current_row < len(logs):
                log_file_path = logs[current_row]['file_path']
                main_window = self.window()
                content = main_window.task_logger.read_log_content(log_file_path)
                log_content.setPlainText(content)

        def refresh_logs():
            main_window = self.window()
            new_logs = main_window.task_logger.get_task_logs(task_name, limit=20)
            log_list.clear()
            for log in new_logs:
                log_list.addItem(f"{log['file_name']} ({log['created_time']})")
            logs.clear()
            logs.extend(new_logs)

        log_list.currentRowChanged.connect(lambda: load_log_content())
        refresh_btn.clicked.connect(refresh_logs)
        close_btn.clicked.connect(dialog.close)

        # 默认选择第一个日志
        if logs:
            log_list.setCurrentRow(0)
            load_log_content()

        dialog.exec()


class LogWidget(QTextEdit):
    """日志显示组件"""
    
    def __init__(self):
        super().__init__()
        self.setup_log_widget()
    
    def setup_log_widget(self) -> None:
        """设置日志组件"""
        self.setReadOnly(True)
        self.setStyleSheet("""
            QTextEdit {
                background-color: #1f2937;
                color: #f9fafb;
                border: 1px solid #374151;
                border-radius: 8px;
                padding: 12px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        
        # 添加示例日志
        self.append("[2024-07-02 12:00:00] 系统启动")
        self.append("[2024-07-02 12:00:01] 加载任务配置")
        self.append("[2024-07-02 12:00:02] 调度器初始化完成")


class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        # 初始化目录管理器
        self.directory_manager = DirectoryManager()
        self.directory_manager.migrate_old_configs()  # 迁移旧配置文件
        self.directory_manager.create_global_config_if_needed()  # 创建全局配置文件

        self.task_scanner = TaskScanner()
        self.config_manager = ConfigManager()
        self.task_logger = TaskLogger()
        self.current_tasks: List[TaskInfo] = []
        self.current_log_file = None  # 当前任务的日志文件

        # 初始化调度器
        self.scheduler = TaskScheduler(
            task_scanner=self.task_scanner,
            config_manager=self.config_manager,
            task_logger=self.task_logger,
            status_callback=self.on_scheduler_status_change
        )

        self.setup_ui()
        self.setup_connections()
        self.setup_ui_refresh()  # 设置UI刷新机制
        self.load_tasks()

        # 根据配置决定是否自动启动调度器
        if self.scheduler.scheduler_config.get_config().auto_start:
            self.start_scheduler()
    
    def setup_ui(self) -> None:
        """设置UI界面"""
        self.setWindowTitle("自动化任务调度管理系统")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置应用样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8fafc;
            }
            QTabWidget::pane {
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                background-color: white;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #f3f4f6;
                padding: 12px 24px;
                margin-right: 4px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                color: #6b7280;
                font-weight: 500;
            }
            QTabBar::tab:selected {
                background-color: white;
                color: #1f2937;
                border-bottom: 2px solid #3b82f6;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建标题区域
        self.create_header(main_layout)
        
        # 创建主要内容区域
        self.create_main_content(main_layout)

        # 创建状态栏
        self.create_status_bar()
    
    def create_header(self, parent_layout: QVBoxLayout) -> None:
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 12px 20px;
                max-height: 60px;
            }
        """)
        header_frame.setMaximumHeight(60)

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 8, 10, 8)

        # 标题
        title_label = QLabel("🤖 自动化任务调度管理系统")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: 600;
                color: #1f2937;
                background: none;
                border: none;
            }
        """)
        
        # 快速操作按钮
        quick_actions = QHBoxLayout()

        self.btn_refresh = ModernButton("� 刷新任务", "primary")
        self.btn_start_scheduler = ModernButton("▶️ 启动调度", "success")
        self.btn_settings = ModernButton("⚙️ 设置", "secondary")

        quick_actions.addWidget(self.btn_refresh)
        quick_actions.addWidget(self.btn_start_scheduler)
        quick_actions.addWidget(self.btn_settings)
        quick_actions.addStretch()
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addLayout(quick_actions)
        
        parent_layout.addWidget(header_frame)
    
    def create_main_content(self, parent_layout: QVBoxLayout) -> None:
        """创建主要内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧：任务列表
        left_widget = self.create_task_list_panel()

        # 右侧：日志监控
        right_widget = self.create_log_panel()

        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([800, 600])  # 左侧800px，右侧600px

        parent_layout.addWidget(splitter)
    
    def create_task_list_panel(self) -> QWidget:
        """创建任务列表面板"""
        task_widget = QWidget()
        task_layout = QVBoxLayout(task_widget)
        task_layout.setContentsMargins(20, 20, 20, 20)

        # 任务列表标题和搜索
        list_header = QHBoxLayout()
        list_title = QLabel("📋 任务列表")
        list_title.setStyleSheet("font-size: 18px; font-weight: 600; color: #1f2937;")

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 搜索任务...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                font-size: 14px;
                max-width: 300px;
            }
            QLineEdit:focus {
                border-color: #3b82f6;
            }
        """)

        list_header.addWidget(list_title)
        list_header.addStretch()
        list_header.addWidget(self.search_input)

        # 任务表格
        self.task_table = TaskTableWidget()

        task_layout.addLayout(list_header)
        task_layout.addWidget(self.task_table)

        return task_widget
      
    def create_log_panel(self) -> QWidget:
        """创建日志监控面板"""
        log_widget = QWidget()
        log_layout = QVBoxLayout(log_widget)
        log_layout.setContentsMargins(20, 20, 20, 20)

        # 日志标题和控制
        log_header = QHBoxLayout()
        log_title = QLabel("📊 系统日志")
        log_title.setStyleSheet("font-size: 18px; font-weight: 600; color: #1f2937;")

        # 日志级别过滤
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["全部", "INFO", "WARNING", "ERROR", "DEBUG"])
        self.log_level_combo.setStyleSheet("""
            QComboBox {
                padding: 6px 12px;
                border: 2px solid #e5e7eb;
                border-radius: 6px;
                font-size: 12px;
                min-width: 80px;
            }
        """)

        self.btn_clear_log = ModernButton("🗑️ 清空", "secondary")
        self.btn_clear_log.setMaximumHeight(32)
        self.btn_export_log = ModernButton("📤 导出", "primary")
        self.btn_export_log.setMaximumHeight(32)

        log_header.addWidget(log_title)
        log_header.addStretch()
        log_header.addWidget(QLabel("级别:"))
        log_header.addWidget(self.log_level_combo)
        log_header.addWidget(self.btn_clear_log)
        log_header.addWidget(self.btn_export_log)

        # 日志显示区域
        self.log_display = LogWidget()

        log_layout.addLayout(log_header)
        log_layout.addWidget(self.log_display)

        return log_widget
    
    def create_status_bar(self) -> None:
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 状态信息
        self.status_bar.showMessage("系统就绪")
        
        # 添加永久部件
        self.task_count_label = QLabel("任务总数: 0")
        self.running_count_label = QLabel("运行中: 0")
        
        self.status_bar.addPermanentWidget(self.task_count_label)
        self.status_bar.addPermanentWidget(self.running_count_label)
    
    def setup_connections(self) -> None:
        """设置信号连接"""
        # 快速操作按钮
        self.btn_refresh.clicked.connect(self.refresh_tasks)
        self.btn_start_scheduler.clicked.connect(self.start_scheduler)
        self.btn_settings.clicked.connect(self.show_settings_dialog)

        # 日志控制按钮
        self.btn_clear_log.clicked.connect(self.clear_log)
        self.btn_export_log.clicked.connect(self.export_log)

    def setup_ui_refresh(self) -> None:
        """设置UI刷新机制"""
        # 创建定时器用于定期刷新
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh_ui)

        # 设置刷新间隔（5秒）
        self.refresh_timer.start(5000)

        # 记录上次刷新时间，避免重复刷新
        self.last_refresh_time = 0

        print("🔄 UI刷新机制已启动 - 定时器间隔: 5秒")

    def auto_refresh_ui(self) -> None:
        """自动刷新UI（定时器触发）"""
        import time
        current_time = time.time()

        # 避免频繁刷新（最小间隔3秒）
        if current_time - self.last_refresh_time < 3:
            return

        self.last_refresh_time = current_time
        self.load_tasks()
        print(f"🔄 定时器触发UI刷新 - {self.get_current_time()}")

    def on_task_completed_refresh(self, task_name: str) -> None:
        """任务完成时触发的刷新"""
        import time
        current_time = time.time()

        # 立即刷新，不受时间间隔限制
        self.last_refresh_time = current_time
        self.load_tasks()
        print(f"🔄 任务完成触发UI刷新: {task_name} - {self.get_current_time()}")

        # 任务表格点击事件 (替代选择事件，因为禁用了选择功能)
        # self.task_table.itemSelectionChanged.connect(self.on_task_selected)  # 已禁用选择
        # 点击事件在TaskTable类中处理

        # 搜索框
        self.search_input.textChanged.connect(self.filter_tasks)

        # 日志级别过滤
        self.log_level_combo.currentTextChanged.connect(self.filter_log)

    def load_tasks(self) -> None:
        """加载任务列表"""
        try:
            self.current_tasks = self.task_scanner.scan_tasks()

            # 清理不存在的任务配置
            existing_task_names = [task.name for task in self.current_tasks]
            self.config_manager.cleanup_missing_tasks(existing_task_names)

            self.task_table.load_tasks(self.current_tasks, self.config_manager)
            self.update_status_bar()

            if self.current_tasks:
                self.log_display.append(f"[{self.get_current_time()}] ✅ 成功加载 {len(self.current_tasks)} 个任务")
            else:
                self.log_display.append(f"[{self.get_current_time()}] ⚠️ 未发现任何任务文件")

        except Exception as e:
            self.log_display.append(f"[{self.get_current_time()}] ❌ 加载任务失败: {e}")

    def refresh_tasks(self) -> None:
        """手动刷新任务列表"""
        import time
        current_time = time.time()

        # 更新最后刷新时间
        self.last_refresh_time = current_time

        self.log_display.append(f"[{self.get_current_time()}] 🔄 手动刷新任务列表...")
        self.load_tasks()

    def filter_tasks(self, text: str) -> None:
        """过滤任务列表"""
        if not text:
            self.task_table.load_tasks(self.current_tasks, self.config_manager)
            return

        filtered_tasks = [
            task for task in self.current_tasks
            if text.lower() in task.name.lower() or
               text.lower() in task.description.lower() or
               text.lower() in task.category.lower()
        ]
        self.task_table.load_tasks(filtered_tasks, self.config_manager)

    def on_task_selected(self) -> None:
        """任务选择事件 (已废弃，使用点击事件)"""
        selected_task = self.task_table.get_selected_task()
        if selected_task:
            self.log_display.append(
                f"[{self.get_current_time()}] 📋 选中任务: {selected_task.name}"
            )

    def on_task_selected_by_click(self, selected_task: TaskInfo) -> None:
        """通过点击选择任务事件"""
        if selected_task:
            self.log_display.append(
                f"[{self.get_current_time()}] 📋 选中任务: {selected_task.name}"
            )

    def update_status_bar(self) -> None:
        """更新状态栏"""
        total_tasks = len(self.current_tasks)

        # 统计不同状态的任务
        enabled_count = 0
        running_count = 0

        for task in self.current_tasks:
            config = self.config_manager.get_task_config(task.name)
            if config.is_enabled:
                enabled_count += 1
            if config.status == "执行中":
                running_count += 1

        self.task_count_label.setText(f"任务总数: {total_tasks}")
        self.running_count_label.setText(f"运行中: {running_count} | 启用: {enabled_count}")

        # 获取调度器状态
        scheduler_status = "🟢 运行中" if self.scheduler.is_running else "🔴 已停止"

        if total_tasks > 0:
            self.status_bar.showMessage(f"调度器: {scheduler_status} | 发现 {total_tasks} 个任务")
        else:
            self.status_bar.showMessage(f"调度器: {scheduler_status} | 未发现任务文件")

    def show_settings_dialog(self) -> None:
        """显示设置对话框"""
        dialog = SettingsDialog(self)
        if dialog.exec():
            self.log_display.append(f"[{self.get_current_time()}] ⚙️ 设置已更新")

    def clear_log(self) -> None:
        """清空日志"""
        self.log_display.clear()
        self.log_display.append(f"[{self.get_current_time()}] 🗑️ 日志已清空")

    def export_log(self) -> None:
        """导出日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"logs/system_log_{timestamp}.txt"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_display.toPlainText())
            self.log_display.append(f"[{self.get_current_time()}] 📤 日志已导出到: {filename}")
        except Exception as e:
            self.log_display.append(f"[{self.get_current_time()}] ❌ 导出失败: {e}")

    def filter_log(self, level: str) -> None:
        """过滤日志级别"""
        # 这里可以实现日志级别过滤逻辑
        self.log_display.append(f"[{self.get_current_time()}] 🔍 日志过滤: {level}")

    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def on_scheduler_status_change(self, message: str) -> None:
        """调度器状态变化回调"""
        self.log_display.append(f"[{self.get_current_time()}] 🔄 {message}")

        # 检查是否是任务完成消息
        if "任务执行成功" in message or "任务完成，状态恢复" in message:
            # 提取任务名称
            if "任务执行成功:" in message:
                task_name = message.split("任务执行成功:")[-1].strip()
            elif "状态恢复:" in message:
                task_name = message.split("状态恢复:")[-1].split("→")[0].strip()
            else:
                task_name = "未知任务"

            # 触发任务完成刷新
            self.on_task_completed_refresh(task_name)

        # 更新状态栏
        self.update_status_bar()

    def start_scheduler(self) -> None:
        """启动调度器"""
        if self.scheduler.start():
            self.log_display.append(f"[{self.get_current_time()}] 🚀 调度器已启动")
            # 更新按钮状态
            self.update_scheduler_buttons()
        else:
            self.log_display.append(f"[{self.get_current_time()}] ❌ 调度器启动失败")

    def stop_scheduler(self) -> None:
        """停止调度器"""
        if self.scheduler.stop():
            self.log_display.append(f"[{self.get_current_time()}] ⏹️ 调度器已停止")
            # 更新按钮状态
            self.update_scheduler_buttons()
        else:
            self.log_display.append(f"[{self.get_current_time()}] ❌ 调度器停止失败")

    def update_scheduler_buttons(self) -> None:
        """更新调度器按钮状态"""
        is_running = self.scheduler.is_running

        # 更新按钮文本、颜色和状态
        if hasattr(self, 'btn_start_scheduler'):
            if is_running:
                self.btn_start_scheduler.setText("⏹️ 停止调度")
                # 设置为停止按钮样式（红色）
                self.btn_start_scheduler.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                   stop: 0 #ef4444, stop: 1 #dc2626);
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: 600;
                        font-size: 13px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                   stop: 0 #dc2626, stop: 1 #b91c1c);
                    }
                    QPushButton:pressed {
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                   stop: 0 #b91c1c, stop: 1 #991b1b);
                    }
                """)
                self.btn_start_scheduler.clicked.disconnect()
                self.btn_start_scheduler.clicked.connect(self.stop_scheduler)
            else:
                self.btn_start_scheduler.setText("▶️ 启动调度")
                # 设置为启动按钮样式（绿色）
                self.btn_start_scheduler.setStyleSheet("""
                    QPushButton {
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                   stop: 0 #22c55e, stop: 1 #16a34a);
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 8px 16px;
                        font-weight: 600;
                        font-size: 13px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                   stop: 0 #16a34a, stop: 1 #15803d);
                    }
                    QPushButton:pressed {
                        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                   stop: 0 #15803d, stop: 1 #166534);
                    }
                """)
                self.btn_start_scheduler.clicked.disconnect()
                self.btn_start_scheduler.clicked.connect(self.start_scheduler)


class SettingsDialog(QDialog):
    """设置对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self) -> None:
        """设置UI"""
        self.setWindowTitle("⚙️ 系统设置")
        self.setFixedSize(500, 400)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)

        # 设置标题
        title = QLabel("⚙️ 系统设置")
        title.setStyleSheet("font-size: 18px; font-weight: 600; color: #1f2937; margin-bottom: 20px;")
        layout.addWidget(title)

        # 设置表单
        form_group = QGroupBox("基本设置")
        form_layout = QGridLayout(form_group)

        # 最大并发任务数
        form_layout.addWidget(QLabel("最大并发任务数:"), 0, 0)
        self.max_tasks_input = QLineEdit("5")
        self.max_tasks_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #e5e7eb;
                border-radius: 6px;
                font-size: 14px;
            }
        """)
        form_layout.addWidget(self.max_tasks_input, 0, 1)

        # 日志保留天数
        form_layout.addWidget(QLabel("日志保留天数:"), 1, 0)
        self.log_days_input = QLineEdit("30")
        self.log_days_input.setStyleSheet(self.max_tasks_input.styleSheet())
        form_layout.addWidget(self.log_days_input, 1, 1)

        # 自动启动
        form_layout.addWidget(QLabel("开机自动启动:"), 2, 0)
        self.auto_start_combo = QComboBox()
        self.auto_start_combo.addItems(["否", "是"])
        self.auto_start_combo.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 2px solid #e5e7eb;
                border-radius: 6px;
                font-size: 14px;
            }
        """)
        form_layout.addWidget(self.auto_start_combo, 2, 1)

        # 任务执行超时时间
        form_layout.addWidget(QLabel("任务执行超时(秒):"), 3, 0)
        self.timeout_input = QLineEdit("300")
        self.timeout_input.setStyleSheet(self.max_tasks_input.styleSheet())
        form_layout.addWidget(self.timeout_input, 3, 1)

        layout.addWidget(form_group)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.btn_save = ModernButton("💾 保存设置", "primary")
        self.btn_cancel = ModernButton("❌ 取消", "secondary")

        self.btn_save.clicked.connect(self.accept)
        self.btn_cancel.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.btn_cancel)
        button_layout.addWidget(self.btn_save)

        layout.addLayout(button_layout)


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("自动化任务调度管理系统")
    app.setApplicationVersion("1.0.0")

    # 创建主窗口
    window = MainWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
