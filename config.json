{"system": {"max_concurrent_tasks": 5, "log_retention_days": 30, "auto_start": false, "check_interval": 60}, "database": {"type": "sqlite", "path": "data/tasks.db"}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_path": "logs/system.log", "max_file_size": "10MB", "backup_count": 5}, "ui": {"theme": "light", "window_size": {"width": 1400, "height": 900}, "auto_refresh_interval": 5}, "tasks": {"default_timeout": 300, "retry_count": 3, "retry_delay": 60}}