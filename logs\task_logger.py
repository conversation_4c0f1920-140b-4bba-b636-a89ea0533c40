"""
任务日志管理器 - 为每个任务创建独立的日志文件夹和日志文件
"""

import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional


class TaskLogger:
    """任务日志管理器"""
    
    def __init__(self, logs_dir: str = "logs"):
        self.logs_dir = Path(logs_dir)
        self.ensure_logs_directory()
    
    def ensure_logs_directory(self) -> None:
        """确保日志目录存在"""
        self.logs_dir.mkdir(exist_ok=True)
        
        # 创建日志目录说明文件
        readme_file = self.logs_dir / "README.md"
        if not readme_file.exists():
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write("""# 任务日志目录

此目录包含所有任务的执行日志，按任务名称分文件夹存储。

## 目录结构
```
logs/
├── 任务名称1/
│   ├── 2024-07-02_12-30-45.log
│   ├── 2024-07-02_13-15-20.log
│   └── ...
├── 任务名称2/
│   ├── 2024-07-02_14-00-10.log
│   └── ...
└── README.md
```

## 日志文件命名规则
- 格式: YYYY-MM-DD_HH-MM-SS.log
- 每次任务执行都会创建一个新的日志文件
""")
    
    def get_task_log_dir(self, task_name: str) -> Path:
        """获取任务的日志目录"""
        # 清理任务名称，移除不合法的文件名字符
        safe_task_name = self.sanitize_filename(task_name)
        task_dir = self.logs_dir / safe_task_name
        task_dir.mkdir(exist_ok=True)
        return task_dir
    
    def sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除不合法字符"""
        # 移除或替换不合法的文件名字符
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # 移除首尾空格和点
        filename = filename.strip(' .')
        
        # 如果文件名为空，使用默认名称
        if not filename:
            filename = "unnamed_task"
        
        return filename
    
    def create_log_file(self, task_name: str) -> Path:
        """为任务创建新的日志文件"""
        task_dir = self.get_task_log_dir(task_name)
        
        # 生成日志文件名：YYYY-MM-DD_HH-MM-SS.log
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        log_file = task_dir / f"{timestamp}.log"
        
        # 创建日志文件并写入头部信息
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"# 任务执行日志\n")
            f.write(f"任务名称: {task_name}\n")
            f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"日志文件: {log_file.name}\n")
            f.write("=" * 50 + "\n\n")
        
        return log_file
    
    def write_log(self, log_file: Path, level: str, message: str) -> None:
        """写入日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}\n"
        
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception as e:
            print(f"❌ 写入日志失败: {e}")
    
    def finish_log(self, log_file: Path, success: bool = True, error_msg: str = "") -> None:
        """完成日志记录"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write("\n" + "=" * 50 + "\n")
                f.write(f"结束时间: {timestamp}\n")
                f.write(f"执行状态: {'✅ 成功' if success else '❌ 失败'}\n")
                if error_msg:
                    f.write(f"错误信息: {error_msg}\n")
                f.write("=" * 50 + "\n")
        except Exception as e:
            print(f"❌ 完成日志记录失败: {e}")
    
    def get_task_logs(self, task_name: str, limit: int = 10) -> List[Dict]:
        """获取任务的最近日志文件列表"""
        task_dir = self.get_task_log_dir(task_name)
        
        if not task_dir.exists():
            return []
        
        # 获取所有日志文件
        log_files = list(task_dir.glob("*.log"))
        
        # 按修改时间排序（最新的在前）
        log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # 限制返回数量
        log_files = log_files[:limit]
        
        # 构建日志信息列表
        logs = []
        for log_file in log_files:
            try:
                stat = log_file.stat()
                logs.append({
                    'file_name': log_file.name,
                    'file_path': str(log_file),
                    'size': stat.st_size,
                    'created_time': datetime.fromtimestamp(stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S'),
                    'modified_time': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
            except Exception as e:
                print(f"❌ 读取日志文件信息失败: {e}")
        
        return logs
    
    def read_log_content(self, log_file_path: str, max_lines: int = 1000) -> str:
        """读取日志文件内容"""
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
                # 如果行数超过限制，只返回最后的行数
                if len(lines) > max_lines:
                    lines = lines[-max_lines:]
                    content = f"... (显示最后 {max_lines} 行)\n" + "".join(lines)
                else:
                    content = "".join(lines)
                
                return content
        except Exception as e:
            return f"❌ 读取日志文件失败: {e}"
    
    def get_all_task_dirs(self) -> List[str]:
        """获取所有任务目录名称"""
        if not self.logs_dir.exists():
            return []
        
        task_dirs = []
        for item in self.logs_dir.iterdir():
            if item.is_dir() and item.name != "__pycache__":
                task_dirs.append(item.name)
        
        return sorted(task_dirs)
    
    def clean_old_logs(self, task_name: str, keep_count: int = 50) -> int:
        """清理旧的日志文件，保留最新的指定数量"""
        task_dir = self.get_task_log_dir(task_name)
        
        if not task_dir.exists():
            return 0
        
        # 获取所有日志文件
        log_files = list(task_dir.glob("*.log"))
        
        # 按修改时间排序（最新的在前）
        log_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # 如果文件数量超过保留数量，删除多余的
        deleted_count = 0
        if len(log_files) > keep_count:
            files_to_delete = log_files[keep_count:]
            for file_to_delete in files_to_delete:
                try:
                    file_to_delete.unlink()
                    deleted_count += 1
                except Exception as e:
                    print(f"❌ 删除日志文件失败: {e}")
        
        return deleted_count


if __name__ == "__main__":
    # 测试日志管理器
    logger = TaskLogger()
    
    # 创建测试日志
    task_name = "文件整理助手"
    log_file = logger.create_log_file(task_name)
    
    # 写入一些测试日志
    logger.write_log(log_file, "INFO", "开始执行文件整理任务")
    logger.write_log(log_file, "INFO", "扫描目录: /downloads")
    logger.write_log(log_file, "INFO", "发现 15 个文件需要整理")
    logger.write_log(log_file, "SUCCESS", "成功整理 15 个文件")
    
    # 完成日志
    logger.finish_log(log_file, success=True)
    
    print(f"✅ 测试日志已创建: {log_file}")
    
    # 获取任务日志列表
    logs = logger.get_task_logs(task_name)
    print(f"📋 任务 '{task_name}' 的日志文件:")
    for log in logs:
        print(f"  - {log['file_name']} ({log['size']} bytes, {log['created_time']})")
