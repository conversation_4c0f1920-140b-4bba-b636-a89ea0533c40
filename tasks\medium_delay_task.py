#!/usr/bin/env python3
"""
中等延迟任务 - 2分钟间隔执行，模拟中等复杂度任务
"""

# 任务名称: 中等延迟任务
# 描述: 每2分钟执行一次，模拟中等复杂度的处理任务
# 作者: 测试
# 版本: 1.0.0
# 分类: 测试

import sys
import os
import time
import random
from datetime import datetime

# 导入全局配置
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared.global_config import G


def run():
    """任务执行函数"""
    start_time = datetime.now()
    print(f"🔄 中等延迟任务开始执行 - {start_time.strftime('%H:%M:%S')}")
    
    # 记录任务统计
    current_count = G.get("test_stats.medium_task.run_count", 0)
    G.test_stats.medium_task.run_count = current_count + 1
    G.test_stats.medium_task.last_run = start_time.strftime("%Y-%m-%d %H:%M:%S")
    
    print(f"📊 这是第 {G.test_stats.medium_task.run_count} 次执行")
    
    # 模拟数据处理工作
    items_to_process = random.randint(3, 7)
    print(f"📦 需要处理 {items_to_process} 个数据项")
    
    processed_items = []
    for i in range(items_to_process):
        item_id = f"ITEM_{random.randint(1000, 9999)}"
        print(f"  🔧 处理数据项 {i+1}/{items_to_process}: {item_id}")
        
        # 模拟处理时间
        process_time = random.uniform(0.8, 1.5)
        time.sleep(process_time)
        
        processed_items.append({
            "id": item_id,
            "process_time": round(process_time, 2),
            "status": "completed"
        })
        
        print(f"    ✓ {item_id} 处理完成 ({process_time:.1f}s)")
    
    # 记录完成时间和统计
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    G.test_stats.medium_task.last_duration = duration
    G.test_stats.medium_task.last_processed_count = len(processed_items)
    G.test_stats.medium_task.total_processed = G.get("test_stats.medium_task.total_processed", 0) + len(processed_items)
    G.test_stats.medium_task.status = "success"
    
    print(f"📈 累计处理数据项: {G.test_stats.medium_task.total_processed}")
    print(f"✅ 中等延迟任务完成 - 耗时: {duration:.1f}秒")
    
    return {
        "status": "success",
        "duration": duration,
        "run_count": G.test_stats.medium_task.run_count,
        "processed_items": len(processed_items),
        "total_processed": G.test_stats.medium_task.total_processed,
        "message": f"成功处理 {len(processed_items)} 个数据项"
    }


if __name__ == "__main__":
    result = run()
    print(f"📋 任务结果: {result}")
